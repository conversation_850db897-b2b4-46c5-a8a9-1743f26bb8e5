package com.tool.converge.common.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ComparisonOperatorEnum 单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@DisplayName("比较运算符枚举测试")
class ComparisonOperatorEnumTest {

    @Test
    @DisplayName("测试GT运算符 - 数值比较")
    void testGT_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.GT.compare("10", "5"));
        assertTrue(ComparisonOperatorEnum.GT.compare("10.5", "10.2"));
        assertFalse(ComparisonOperatorEnum.GT.compare("5", "10"));
        assertFalse(ComparisonOperatorEnum.GT.compare("10", "10"));
    }

    @Test
    @DisplayName("测试GT运算符 - 字符串比较")
    void testGT_StringComparison() {
        assertTrue(ComparisonOperatorEnum.GT.compare("b", "a"));
        assertTrue(ComparisonOperatorEnum.GT.compare("abc", "abb"));
        assertFalse(ComparisonOperatorEnum.GT.compare("a", "b"));
        assertFalse(ComparisonOperatorEnum.GT.compare("abc", "abc"));
    }

    @Test
    @DisplayName("测试GTE运算符 - 数值比较")
    void testGTE_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.GTE.compare("10", "5"));
        assertTrue(ComparisonOperatorEnum.GTE.compare("10", "10"));
        assertTrue(ComparisonOperatorEnum.GTE.compare("10.5", "10.2"));
        assertFalse(ComparisonOperatorEnum.GTE.compare("5", "10"));
    }

    @Test
    @DisplayName("测试GTE运算符 - 字符串比较")
    void testGTE_StringComparison() {
        assertTrue(ComparisonOperatorEnum.GTE.compare("b", "a"));
        assertTrue(ComparisonOperatorEnum.GTE.compare("abc", "abc"));
        assertFalse(ComparisonOperatorEnum.GTE.compare("a", "b"));
    }

    @Test
    @DisplayName("测试EQ运算符 - 数值比较")
    void testEQ_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.EQ.compare("10", "10"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("10.0", "10"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("0", "0.0"));
        assertFalse(ComparisonOperatorEnum.EQ.compare("10", "5"));
        assertFalse(ComparisonOperatorEnum.EQ.compare("10.1", "10.2"));
    }

    @Test
    @DisplayName("测试EQ运算符 - 字符串比较")
    void testEQ_StringComparison() {
        assertTrue(ComparisonOperatorEnum.EQ.compare("abc", "abc"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("", ""));
        assertFalse(ComparisonOperatorEnum.EQ.compare("abc", "def"));
        assertFalse(ComparisonOperatorEnum.EQ.compare("ABC", "abc"));
    }

    @Test
    @DisplayName("测试EQ运算符 - null值处理")
    void testEQ_NullValues() {
        assertTrue(ComparisonOperatorEnum.EQ.compare(null, null));
        assertFalse(ComparisonOperatorEnum.EQ.compare(null, "abc"));
        assertFalse(ComparisonOperatorEnum.EQ.compare("abc", null));
    }

    @Test
    @DisplayName("测试LT运算符 - 数值比较")
    void testLT_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.LT.compare("5", "10"));
        assertTrue(ComparisonOperatorEnum.LT.compare("10.2", "10.5"));
        assertFalse(ComparisonOperatorEnum.LT.compare("10", "5"));
        assertFalse(ComparisonOperatorEnum.LT.compare("10", "10"));
    }

    @Test
    @DisplayName("测试LT运算符 - 字符串比较")
    void testLT_StringComparison() {
        assertTrue(ComparisonOperatorEnum.LT.compare("a", "b"));
        assertTrue(ComparisonOperatorEnum.LT.compare("abb", "abc"));
        assertFalse(ComparisonOperatorEnum.LT.compare("b", "a"));
        assertFalse(ComparisonOperatorEnum.LT.compare("abc", "abc"));
    }

    @Test
    @DisplayName("测试LTE运算符 - 数值比较")
    void testLTE_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.LTE.compare("5", "10"));
        assertTrue(ComparisonOperatorEnum.LTE.compare("10", "10"));
        assertTrue(ComparisonOperatorEnum.LTE.compare("10.2", "10.5"));
        assertFalse(ComparisonOperatorEnum.LTE.compare("10", "5"));
    }

    @Test
    @DisplayName("测试LTE运算符 - 字符串比较")
    void testLTE_StringComparison() {
        assertTrue(ComparisonOperatorEnum.LTE.compare("a", "b"));
        assertTrue(ComparisonOperatorEnum.LTE.compare("abc", "abc"));
        assertFalse(ComparisonOperatorEnum.LTE.compare("b", "a"));
    }

    @Test
    @DisplayName("测试NE运算符 - 数值比较")
    void testNE_NumericComparison() {
        assertTrue(ComparisonOperatorEnum.NE.compare("10", "5"));
        assertTrue(ComparisonOperatorEnum.NE.compare("10.1", "10.2"));
        assertFalse(ComparisonOperatorEnum.NE.compare("10", "10"));
        assertFalse(ComparisonOperatorEnum.NE.compare("10.0", "10"));
    }

    @Test
    @DisplayName("测试NE运算符 - 字符串比较")
    void testNE_StringComparison() {
        assertTrue(ComparisonOperatorEnum.NE.compare("abc", "def"));
        assertTrue(ComparisonOperatorEnum.NE.compare("ABC", "abc"));
        assertFalse(ComparisonOperatorEnum.NE.compare("abc", "abc"));
        assertFalse(ComparisonOperatorEnum.NE.compare("", ""));
    }

    @Test
    @DisplayName("测试空值和空白字符串处理")
    void testBlankAndEmptyValues() {
        // 空白字符串应该返回false（除了EQ的特殊处理）
        assertFalse(ComparisonOperatorEnum.GT.compare("", "5"));
        assertFalse(ComparisonOperatorEnum.GT.compare("5", ""));
        assertFalse(ComparisonOperatorEnum.GT.compare("", ""));
        
        assertFalse(ComparisonOperatorEnum.GTE.compare(" ", "5"));
        assertFalse(ComparisonOperatorEnum.LT.compare("5", " "));
        assertFalse(ComparisonOperatorEnum.LTE.compare(" ", " "));
        
        // EQ对空字符串的特殊处理
        assertTrue(ComparisonOperatorEnum.EQ.compare("", ""));
        assertFalse(ComparisonOperatorEnum.EQ.compare("", "5"));
    }

    @ParameterizedTest
    @CsvSource({
        "GT, true",
        "GTE, true", 
        "EQ, true",
        "LT, true",
        "LTE, true",
        "NE, true",
        "INVALID, false",
        "'', false"
    })
    @DisplayName("测试fromCode方法")
    void testFromCode(String code, boolean shouldExist) {
        ComparisonOperatorEnum result = ComparisonOperatorEnum.fromCode(code);
        if (shouldExist) {
            assertNotNull(result);
            assertEquals(code, result.getCode());
        } else {
            assertNull(result);
        }
    }

    @Test
    @DisplayName("测试fromCode方法 - 大小写不敏感")
    void testFromCode_CaseInsensitive() {
        assertEquals(ComparisonOperatorEnum.GT, ComparisonOperatorEnum.fromCode("gt"));
        assertEquals(ComparisonOperatorEnum.EQ, ComparisonOperatorEnum.fromCode("eq"));
        assertEquals(ComparisonOperatorEnum.LTE, ComparisonOperatorEnum.fromCode("lte"));
    }

    @ParameterizedTest
    @CsvSource({
        "GT, true",
        "gte, true",
        "Eq, true", 
        "INVALID, false",
        "'', false"
    })
    @DisplayName("测试isValidCode方法")
    void testIsValidCode(String code, boolean expected) {
        assertEquals(expected, ComparisonOperatorEnum.isValidCode(code));
    }

    @Test
    @DisplayName("测试枚举基本属性")
    void testEnumProperties() {
        assertEquals("GT", ComparisonOperatorEnum.GT.getCode());
        assertEquals("大于", ComparisonOperatorEnum.GT.getName());
        
        assertEquals("EQ", ComparisonOperatorEnum.EQ.getCode());
        assertEquals("等于", ComparisonOperatorEnum.EQ.getName());
        
        assertEquals("NE", ComparisonOperatorEnum.NE.getCode());
        assertEquals("不等于", ComparisonOperatorEnum.NE.getName());
    }

    @Test
    @DisplayName("测试边界情况 - 大数值")
    void testLargeNumbers() {
        assertTrue(ComparisonOperatorEnum.GT.compare("999999999999", "999999999998"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("999999999999", "999999999999"));
        assertFalse(ComparisonOperatorEnum.LT.compare("999999999999", "999999999998"));
    }

    @Test
    @DisplayName("测试边界情况 - 小数精度")
    void testDecimalPrecision() {
        assertTrue(ComparisonOperatorEnum.GT.compare("1.0001", "1.0000"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("1.0000", "1.0"));
        assertFalse(ComparisonOperatorEnum.NE.compare("1.0000", "1.0"));
    }

    @Test
    @DisplayName("测试边界情况 - 负数")
    void testNegativeNumbers() {
        assertTrue(ComparisonOperatorEnum.GT.compare("-1", "-2"));
        assertTrue(ComparisonOperatorEnum.LT.compare("-2", "-1"));
        assertTrue(ComparisonOperatorEnum.EQ.compare("-10", "-10"));
        assertFalse(ComparisonOperatorEnum.GT.compare("-2", "-1"));
    }
}
