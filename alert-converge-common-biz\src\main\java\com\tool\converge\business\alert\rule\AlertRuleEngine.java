package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.alert.rule.exception.RuleEngineException;
import com.tool.converge.business.alert.rule.fallback.RuleEngineFallbackHandler;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 预警规则引擎实现类
 * 用于评估预警事件是否匹配配置的规则
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class AlertRuleEngine implements RuleEngineService {

    @Resource
    private RulesService rulesService;

    @Resource
    private PayloadParser payloadParser;

    @Resource
    private CapitalMatcher capitalMatcher;

    @Resource
    private RuleProcessor ruleProcessor;

    @Resource
    private RuleEngineFallbackHandler fallbackHandler;

    /**
     * 评估预警事件是否匹配规则
     * 
     * @param alertEventDO 预警事件
     * @return 规则评估结果
     */
    public RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO) {
        long startTime = System.currentTimeMillis();

        try {
            // 参数验证
            if (!isValidAlertEvent(alertEventDO)) {
                throw RuleEngineException.validationError("预警事件数据不完整");
            }

            log.info("开始评估预警事件规则，事件ID：{}，事件编号：{}", alertEventDO.getId(), alertEventDO.getEventId());

            // 1. 解析payload数据
            Map<String, String> payloadMap;
            try {
                payloadMap = payloadParser.parsePayload(alertEventDO.getPayload());
                if (payloadMap == null || payloadMap.isEmpty()) {
                    throw RuleEngineException.parseError("payload解析失败或为空");
                }
            } catch (Exception e) {
                throw RuleEngineException.parseError("payload解析异常", e);
            }

            // 2. 查询启用的规则
            List<RulesDO> enabledRules;
            try {
                enabledRules = rulesService.list(
                    new LambdaQueryWrapper<RulesDO>()
                        .eq(RulesDO::getRuleStatus, "0") // 0-启用
                        .eq(RulesDO::getDeleted, false)
                );
            } catch (Exception e) {
                throw RuleEngineException.databaseError("查询启用规则异常", e);
            }

            if (CollectionUtils.isEmpty(enabledRules)) {
                log.debug("未找到启用的规则，事件ID：{}", alertEventDO.getId());
                return RuleEvaluationResult.failure("未找到启用的规则");
            }

            log.debug("找到{}个启用的规则，事件ID：{}", enabledRules.size(), alertEventDO.getId());

            // 3. 逐个评估规则
            List<RuleProcessDetail> processDetails = new ArrayList<>();
            List<RulesDO> matchedRules = new ArrayList<>();

            for (RulesDO rule : enabledRules) {
                try {
                    // 3.1 资方匹配验证
                    boolean capitalMatched = capitalMatcher.matchCapital(payloadMap, rule);
                    
                    RuleProcessDetail detail = RuleProcessDetail.builder()
                        .ruleId(rule.getId())
                        .ruleName(rule.getRuleName())
                        .ruleCode(rule.getRuleCode())
                        .ruleMatching(rule.getRuleMatching())
                        .capitalMatched(capitalMatched)
                        .processStartTime(System.currentTimeMillis())
                        .build();

                    if (!capitalMatched) {
                        detail.setCapitalMatchMessage("资方不匹配");
                        detail.setStatus(com.tool.converge.business.alert.rule.model.ProcessStatus.SKIPPED);
                        detail.setMessage("资方不匹配，跳过规则评估");
                        detail.setProcessEndTime(System.currentTimeMillis());
                        processDetails.add(detail);
                        
                        log.debug("规则资方不匹配，跳过评估，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
                        continue;
                    }

                    detail.setCapitalMatchMessage("资方匹配成功");

                    // 3.2 条件评估
                    RuleProcessDetail ruleDetail = ruleProcessor.processRuleWithDetail(rule, payloadMap);
                    
                    // 合并资方匹配信息到规则处理详情
                    ruleDetail.setCapitalMatched(capitalMatched);
                    ruleDetail.setCapitalMatchMessage(detail.getCapitalMatchMessage());
                    
                    processDetails.add(ruleDetail);

                    if (ruleDetail.isMatched()) {
                        matchedRules.add(rule);
                        log.info("规则匹配成功，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
                    } else {
                        log.debug("规则匹配失败，规则ID：{}，规则名称：{}，原因：{}", 
                            rule.getId(), rule.getRuleName(), ruleDetail.getMessage());
                    }

                } catch (Exception e) {
                    log.error("规则评估异常，规则ID：{}，规则名称：{}，错误：{}", 
                        rule.getId(), rule.getRuleName(), e.getMessage(), e);
                    
                    RuleProcessDetail errorDetail = RuleProcessDetail.error(
                        rule.getId(), rule.getRuleName(), rule.getRuleCode(), 
                        "规则评估异常：" + e.getMessage());
                    processDetails.add(errorDetail);
                }
            }

            // 4. 构建评估结果
            long endTime = System.currentTimeMillis();
            
            if (CollectionUtils.isNotEmpty(matchedRules)) {
                log.info("预警事件规则评估完成，匹配{}个规则，事件ID：{}，耗时：{}ms",
                    matchedRules.size(), alertEventDO.getId(), endTime - startTime);

                // 使用第一个匹配的规则创建成功结果
                RulesDO firstMatchedRule = matchedRules.get(0);
                RuleEvaluationResult result = RuleEvaluationResult.success(
                    firstMatchedRule.getId(),
                    firstMatchedRule.getRuleName(),
                    firstMatchedRule.getRuleCode(),
                    "HIGH" // 默认预警级别，可以根据实际需求调整
                );
                result.setProcessDetails(processDetails);
                return result;
            } else {
                log.info("预警事件规则评估完成，无匹配规则，事件ID：{}，耗时：{}ms",
                    alertEventDO.getId(), endTime - startTime);

                RuleEvaluationResult result = RuleEvaluationResult.failure("无匹配规则");
                result.setProcessDetails(processDetails);
                return result;
            }

        } catch (Exception e) {
            log.error("预警事件规则评估异常，事件ID：{}，错误：{}", alertEventDO.getId(), e.getMessage(), e);

            // 使用降级处理器处理异常
            if (fallbackHandler.shouldFallback(e)) {
                return fallbackHandler.handleEvaluationException(alertEventDO, e);
            } else {
                // 对于不需要降级的异常（如参数验证异常），直接抛出
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                } else {
                    throw new RuntimeException("规则评估异常", e);
                }
            }
        }
    }

    /**
     * 评估指定规则是否匹配预警事件
     * 
     * @param alertEventDO 预警事件
     * @param ruleId 规则ID
     * @return 规则评估结果
     */
    public RuleEvaluationResult evaluateRule(AlertEventDO alertEventDO, Long ruleId) {
        try {
            // 参数验证
            if (!isValidAlertEvent(alertEventDO)) {
                throw RuleEngineException.validationError("预警事件数据不完整");
            }
            if (ruleId == null) {
                throw RuleEngineException.validationError("规则ID不能为空");
            }

            log.info("开始评估指定规则，事件ID：{}，规则ID：{}", alertEventDO.getId(), ruleId);

            // 1. 查询规则
            RulesDO rule;
            try {
                rule = rulesService.getById(ruleId);
            } catch (Exception e) {
                throw RuleEngineException.databaseError("查询规则异常，规则ID：" + ruleId, e);
            }

            if (rule == null) {
                throw RuleEngineException.ruleConfigError("规则不存在，规则ID：" + ruleId);
            }

            if (!"0".equals(rule.getRuleStatus())) {
                return RuleEvaluationResult.failure("规则未启用，规则ID：" + ruleId);
            }

            // 2. 解析payload数据
            Map<String, String> payloadMap = payloadParser.parsePayload(alertEventDO.getPayload());
            if (payloadMap == null || payloadMap.isEmpty()) {
                return RuleEvaluationResult.failure("payload解析失败或为空");
            }

            // 3. 资方匹配验证
            boolean capitalMatched = capitalMatcher.matchCapital(payloadMap, rule);
            if (!capitalMatched) {
                RuleProcessDetail detail = RuleProcessDetail.skipped(
                    rule.getId(), rule.getRuleName(), rule.getRuleCode(), "资方不匹配");
                detail.setCapitalMatched(false);
                detail.setCapitalMatchMessage("资方不匹配");

                RuleEvaluationResult result = RuleEvaluationResult.failure("资方不匹配");
                List<RuleProcessDetail> details = new ArrayList<>();
                details.add(detail);
                result.setProcessDetails(details);
                return result;
            }

            // 4. 条件评估
            RuleProcessDetail ruleDetail = ruleProcessor.processRuleWithDetail(rule, payloadMap);
            ruleDetail.setCapitalMatched(capitalMatched);
            ruleDetail.setCapitalMatchMessage("资方匹配成功");

            if (ruleDetail.isMatched()) {
                RuleEvaluationResult result = RuleEvaluationResult.success(
                    rule.getId(),
                    rule.getRuleName(),
                    rule.getRuleCode(),
                    "HIGH" // 默认预警级别
                );
                List<RuleProcessDetail> details = new ArrayList<>();
                details.add(ruleDetail);
                result.setProcessDetails(details);
                return result;
            } else {
                RuleEvaluationResult result = RuleEvaluationResult.failure(
                    "规则匹配失败：" + ruleDetail.getMessage());
                List<RuleProcessDetail> details = new ArrayList<>();
                details.add(ruleDetail);
                result.setProcessDetails(details);
                return result;
            }

        } catch (Exception e) {
            log.error("指定规则评估异常，事件ID：{}，规则ID：{}，错误：{}",
                alertEventDO.getId(), ruleId, e.getMessage(), e);

            // 使用降级处理器处理异常
            if (fallbackHandler.shouldFallback(e)) {
                return fallbackHandler.handleEvaluationException(alertEventDO, e);
            } else {
                // 对于不需要降级的异常，直接抛出
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                } else {
                    throw new RuntimeException("规则评估异常", e);
                }
            }
        }
    }

    /**
     * 检查预警事件数据的完整性
     * 
     * @param alertEventDO 预警事件
     * @return 检查结果
     */
    public boolean isValidAlertEvent(AlertEventDO alertEventDO) {
        if (alertEventDO == null) {
            return false;
        }

        if (alertEventDO.getId() == null) {
            return false;
        }

        if (StringUtils.isBlank(alertEventDO.getPayload())) {
            return false;
        }

        return true;
    }

    /**
     * 获取启用的规则数量
     * 
     * @return 启用的规则数量
     */
    public int getEnabledRuleCount() {
        try {
            return (int) rulesService.count(
                new LambdaQueryWrapper<RulesDO>()
                    .eq(RulesDO::getRuleStatus, "0") // 0-启用
                    .eq(RulesDO::getDeleted, false)
            );
        } catch (Exception e) {
            log.error("查询启用规则数量异常，错误：{}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取所有启用的规则
     * 
     * @return 启用的规则列表
     */
    public List<RulesDO> getEnabledRules() {
        try {
            return rulesService.list(
                new LambdaQueryWrapper<RulesDO>()
                    .eq(RulesDO::getRuleStatus, "0") // 0-启用
                    .eq(RulesDO::getDeleted, false)
            );
        } catch (Exception e) {
            log.error("查询启用规则列表异常，错误：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<RuleEvaluationResult> batchEvaluateRules(List<AlertEventDO> alertEvents) {
        if (alertEvents == null || alertEvents.isEmpty()) {
            throw RuleEngineException.validationError("预警事件列表不能为空");
        }

        List<RuleEvaluationResult> results = new ArrayList<>();

        try {
            for (AlertEventDO alertEvent : alertEvents) {
                try {
                    RuleEvaluationResult result = evaluateRules(alertEvent);
                    results.add(result);
                } catch (Exception e) {
                    log.error("批量评估规则异常，事件ID：{}，错误：{}", alertEvent.getId(), e.getMessage(), e);

                    // 使用降级处理器处理单个事件的异常
                    if (fallbackHandler.shouldFallback(e)) {
                        RuleEvaluationResult fallbackResult = fallbackHandler.handleEvaluationException(alertEvent, e);
                        results.add(fallbackResult);
                    } else {
                        // 对于不需要降级的异常，添加错误结果
                        RuleEvaluationResult errorResult = RuleEvaluationResult.error("批量评估异常：" + e.getMessage());
                        results.add(errorResult);
                    }
                }
            }

            log.info("批量评估规则完成，处理事件数量：{}，成功数量：{}",
                alertEvents.size(),
                results.stream().mapToInt(r -> r.isMatched() ? 1 : 0).sum());

            return results;

        } catch (Exception e) {
            log.error("批量评估规则整体异常，错误：{}", e.getMessage(), e);

            // 使用降级处理器处理批量异常
            if (fallbackHandler.shouldFallback(e)) {
                return fallbackHandler.handleBatchEvaluationException(alertEvents, e);
            } else {
                throw e;
            }
        }
    }

    @Override
    public java.util.Map<String, Object> getEvaluationStatistics() {
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();

        try {
            // 获取启用规则数量
            int enabledRuleCount = getEnabledRuleCount();
            statistics.put("enabledRuleCount", enabledRuleCount);

            // 获取系统运行时间
            statistics.put("systemUptime", System.currentTimeMillis());

            // 获取JVM内存使用情况
            Runtime runtime = Runtime.getRuntime();
            statistics.put("totalMemory", runtime.totalMemory());
            statistics.put("freeMemory", runtime.freeMemory());
            statistics.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());

            log.debug("获取规则评估统计信息成功，启用规则数量：{}", enabledRuleCount);

        } catch (Exception e) {
            log.error("获取规则评估统计信息异常，错误：{}", e.getMessage(), e);
            statistics.put("error", "获取统计信息异常：" + e.getMessage());
        }

        return statistics;
    }

    @Override
    public void clearCache() {
        try {
            // 这里可以清理相关缓存，目前暂时只记录日志
            log.info("清理规则引擎缓存完成");
        } catch (Exception e) {
            log.error("清理规则引擎缓存异常，错误：{}", e.getMessage(), e);
        }
    }

    @Override
    public void warmUp() {
        try {
            log.info("开始预热规则引擎");

            // 预加载启用的规则
            List<RulesDO> enabledRules = getEnabledRules();
            log.info("预加载启用规则数量：{}", enabledRules.size());

            // 预热完成
            log.info("规则引擎预热完成");

        } catch (Exception e) {
            log.error("规则引擎预热异常，错误：{}", e.getMessage(), e);
        }
    }
}
