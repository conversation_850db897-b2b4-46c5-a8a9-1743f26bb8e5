package com.tool.converge.business.alert.rule;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tool.converge.repository.domain.alert.bo.AlertEventReportBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事件数据解析器
 * 用于解析预警事件的payload数据，将JSON字符串转换为便于查找的Map结构
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class PayloadParser {

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 解析payload JSON字符串为Map<String, String>
     * 
     * @param payloadJson JSON字符串
     * @return code-value映射表，如果解析失败返回空Map
     */
    public Map<String, String> parsePayload(String payloadJson) {
        if (StringUtils.isBlank(payloadJson)) {
            log.warn("Payload JSON字符串为空");
            return Collections.emptyMap();
        }

        try {
            // 尝试解析为PayloadItem列表
            List<AlertEventReportBO.PayloadItem> payloadItems = objectMapper.readValue(
                payloadJson, 
                new TypeReference<List<AlertEventReportBO.PayloadItem>>() {}
            );

            if (payloadItems == null || payloadItems.isEmpty()) {
                log.warn("解析后的PayloadItem列表为空");
                return Collections.emptyMap();
            }

            // 转换为Map结构便于查找
            Map<String, String> payloadMap = new HashMap<>();
            for (AlertEventReportBO.PayloadItem item : payloadItems) {
                if (item != null && StringUtils.isNotBlank(item.getCode())) {
                    // 使用code作为key，value作为值
                    payloadMap.put(item.getCode(), item.getValue());
                    log.debug("解析payload项: code={}, name={}, value={}", 
                        item.getCode(), item.getName(), item.getValue());
                }
            }

            log.info("成功解析payload，共{}个数据项", payloadMap.size());
            return payloadMap;

        } catch (Exception e) {
            log.error("解析payload JSON失败: {}", payloadJson, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据code快速查找value
     * 
     * @param payloadMap payload映射表
     * @param code 要查找的code
     * @return 对应的value，如果不存在返回null
     */
    public String getValue(Map<String, String> payloadMap, String code) {
        if (payloadMap == null || StringUtils.isBlank(code)) {
            return null;
        }
        return payloadMap.get(code);
    }

    /**
     * 检查payload中是否包含指定的code
     * 
     * @param payloadMap payload映射表
     * @param code 要检查的code
     * @return 是否包含
     */
    public boolean containsCode(Map<String, String> payloadMap, String code) {
        if (payloadMap == null || StringUtils.isBlank(code)) {
            return false;
        }
        return payloadMap.containsKey(code);
    }

    /**
     * 获取payload中所有的code列表
     * 
     * @param payloadMap payload映射表
     * @return code列表
     */
    public List<String> getAllCodes(Map<String, String> payloadMap) {
        if (payloadMap == null || payloadMap.isEmpty()) {
            return Collections.emptyList();
        }
        return new java.util.ArrayList<>(payloadMap.keySet());
    }

    /**
     * 验证payload数据的完整性
     * 检查是否包含必要的字段
     * 
     * @param payloadMap payload映射表
     * @param requiredCodes 必需的code列表
     * @return 验证结果，包含缺失的字段信息
     */
    public PayloadValidationResult validatePayload(Map<String, String> payloadMap, List<String> requiredCodes) {
        PayloadValidationResult result = new PayloadValidationResult();

        // 如果没有必需字段要求，则验证通过
        if (requiredCodes == null || requiredCodes.isEmpty()) {
            result.setValid(true);
            result.setMessage("无必需字段要求");
            return result;
        }

        // 如果有必需字段要求，但payload为空，则验证失败
        if (payloadMap == null || payloadMap.isEmpty()) {
            result.setValid(false);
            result.setMessage("Payload数据为空");
            return result;
        }

        // 检查必需字段
        for (String requiredCode : requiredCodes) {
            if (!payloadMap.containsKey(requiredCode)) {
                result.setValid(false);
                result.getMissingCodes().add(requiredCode);
            }
        }

        if (result.isValid()) {
            result.setMessage("Payload数据验证通过");
        } else {
            result.setMessage("缺少必需字段: " + String.join(", ", result.getMissingCodes()));
        }

        return result;
    }

    /**
     * Payload验证结果
     */
    public static class PayloadValidationResult {
        private boolean valid = true;
        private String message;
        private List<String> missingCodes = new java.util.ArrayList<>();

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public List<String> getMissingCodes() {
            return missingCodes;
        }

        public void setMissingCodes(List<String> missingCodes) {
            this.missingCodes = missingCodes;
        }
    }
}
