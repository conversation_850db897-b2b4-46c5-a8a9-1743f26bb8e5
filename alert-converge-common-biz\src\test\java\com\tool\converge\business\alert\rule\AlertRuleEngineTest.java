package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.alert.rule.model.ProcessStatus;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * AlertRuleEngine单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class AlertRuleEngineTest {

    @Mock
    private RulesService rulesService;

    @Mock
    private PayloadParser payloadParser;

    @Mock
    private CapitalMatcher capitalMatcher;

    @Mock
    private RuleProcessor ruleProcessor;

    @InjectMocks
    private AlertRuleEngine alertRuleEngine;

    private AlertEventDO alertEventDO;
    private List<RulesDO> enabledRules;
    private Map<String, String> payloadMap;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        alertEventDO = AlertEventDO.builder()
            .id(1L)
            .eventId("EVENT_001")
            .payload("{\"fund_provider_name\":\"测试资方\",\"amount\":\"100.50\"}")
            .build();

        enabledRules = new ArrayList<>();
        enabledRules.add(RulesDO.builder()
            .id(1L)
            .ruleName("测试规则1")
            .ruleCode("TEST_RULE_1")
            .ruleStatus("0")
            .ruleMatching("1")
            .applyInvestor("1,2,3")
            .build());
        enabledRules.add(RulesDO.builder()
            .id(2L)
            .ruleName("测试规则2")
            .ruleCode("TEST_RULE_2")
            .ruleStatus("0")
            .ruleMatching("0")
            .applyInvestor("1,2,3")
            .build());

        payloadMap = new HashMap<>();
        payloadMap.put("fund_provider_name", "测试资方");
        payloadMap.put("amount", "100.50");
    }

    @Test
    void testEvaluateRules_Success() {
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟查询启用规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(enabledRules);
        
        // 模拟资方匹配成功
        when(capitalMatcher.matchCapital(any(Map.class), any(RulesDO.class))).thenReturn(true);
        
        // 模拟规则处理成功
        RuleProcessDetail successDetail = RuleProcessDetail.success(1L, "测试规则1", "TEST_RULE_1", "规则匹配成功");
        when(ruleProcessor.processRuleWithDetail(any(RulesDO.class), any(Map.class))).thenReturn(successDetail);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isMatched());
        assertEquals(1L, result.getMatchedRuleId());
        assertEquals("测试规则1", result.getMatchedRuleName());
        assertEquals("TEST_RULE_1", result.getMatchedRuleCode());
        assertNotNull(result.getProcessDetails());
        assertFalse(result.getProcessDetails().isEmpty());

        // 验证方法调用
        verify(payloadParser, times(1)).parsePayload(alertEventDO.getPayload());
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(capitalMatcher, times(2)).matchCapital(any(Map.class), any(RulesDO.class));
        verify(ruleProcessor, times(2)).processRuleWithDetail(any(RulesDO.class), any(Map.class));
    }

    @Test
    void testEvaluateRules_NoMatchedRules() {
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟查询启用规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(enabledRules);
        
        // 模拟资方匹配成功
        when(capitalMatcher.matchCapital(any(Map.class), any(RulesDO.class))).thenReturn(true);
        
        // 模拟规则处理失败
        RuleProcessDetail failedDetail = RuleProcessDetail.failure(1L, "测试规则1", "TEST_RULE_1", "规则匹配失败");
        when(ruleProcessor.processRuleWithDetail(any(RulesDO.class), any(Map.class))).thenReturn(failedDetail);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("无匹配规则"));
        assertNotNull(result.getProcessDetails());
        assertEquals(2, result.getProcessDetails().size());
    }

    @Test
    void testEvaluateRules_CapitalNotMatched() {
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟查询启用规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(enabledRules);
        
        // 模拟资方匹配失败
        when(capitalMatcher.matchCapital(any(Map.class), any(RulesDO.class))).thenReturn(false);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("无匹配规则"));
        assertNotNull(result.getProcessDetails());
        assertEquals(2, result.getProcessDetails().size());
        
        // 验证所有处理详情都是跳过状态
        for (RuleProcessDetail detail : result.getProcessDetails()) {
            assertEquals(ProcessStatus.SKIPPED, detail.getStatus());
            assertTrue(detail.getMessage().contains("资方不匹配"));
        }

        // 验证不会调用规则处理器
        verify(ruleProcessor, never()).processRuleWithDetail(any(RulesDO.class), any(Map.class));
    }

    @Test
    void testEvaluateRules_EmptyPayload() {
        // 模拟payload解析返回空
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(new HashMap<>());

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("payload解析失败或为空"));

        // 验证不会查询规则
        verify(rulesService, never()).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testEvaluateRules_NoEnabledRules() {
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟查询启用规则返回空列表
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("未找到启用的规则"));
    }

    @Test
    void testEvaluateRules_Exception() {
        // 模拟payload解析抛出异常
        when(payloadParser.parsePayload(alertEventDO.getPayload()))
            .thenThrow(new RuntimeException("解析异常"));

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("规则评估异常"));
    }

    @Test
    void testEvaluateRule_Success() {
        // 模拟查询规则
        RulesDO rule = enabledRules.get(0);
        when(rulesService.getById(1L)).thenReturn(rule);
        
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟资方匹配成功
        when(capitalMatcher.matchCapital(any(Map.class), any(RulesDO.class))).thenReturn(true);
        
        // 模拟规则处理成功
        RuleProcessDetail successDetail = RuleProcessDetail.success(1L, "测试规则1", "TEST_RULE_1", "规则匹配成功");
        when(ruleProcessor.processRuleWithDetail(any(RulesDO.class), any(Map.class))).thenReturn(successDetail);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRule(alertEventDO, 1L);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isMatched());
        assertEquals(1L, result.getMatchedRuleId());
        assertEquals("测试规则1", result.getMatchedRuleName());
        assertEquals("TEST_RULE_1", result.getMatchedRuleCode());
    }

    @Test
    void testEvaluateRule_RuleNotFound() {
        // 模拟查询规则返回null
        when(rulesService.getById(1L)).thenReturn(null);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRule(alertEventDO, 1L);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("规则不存在"));
    }

    @Test
    void testEvaluateRule_RuleDisabled() {
        // 模拟查询规则，但规则未启用
        RulesDO disabledRule = RulesDO.builder()
            .id(1L)
            .ruleName("测试规则1")
            .ruleCode("TEST_RULE_1")
            .ruleStatus("1") // 1-关闭
            .build();
        when(rulesService.getById(1L)).thenReturn(disabledRule);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRule(alertEventDO, 1L);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("规则未启用"));
    }

    @Test
    void testEvaluateRule_CapitalNotMatched() {
        // 模拟查询规则
        RulesDO rule = enabledRules.get(0);
        when(rulesService.getById(1L)).thenReturn(rule);
        
        // 模拟payload解析成功
        when(payloadParser.parsePayload(alertEventDO.getPayload())).thenReturn(payloadMap);
        
        // 模拟资方匹配失败
        when(capitalMatcher.matchCapital(any(Map.class), any(RulesDO.class))).thenReturn(false);

        // 执行测试
        RuleEvaluationResult result = alertRuleEngine.evaluateRule(alertEventDO, 1L);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("资方不匹配"));
    }

    @Test
    void testIsValidAlertEvent_Valid() {
        // 执行测试
        boolean result = alertRuleEngine.isValidAlertEvent(alertEventDO);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsValidAlertEvent_NullEvent() {
        // 执行测试
        boolean result = alertRuleEngine.isValidAlertEvent(null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidAlertEvent_NullId() {
        // 设置ID为null
        alertEventDO.setId(null);

        // 执行测试
        boolean result = alertRuleEngine.isValidAlertEvent(alertEventDO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidAlertEvent_EmptyPayload() {
        // 设置payload为空
        alertEventDO.setPayload("");

        // 执行测试
        boolean result = alertRuleEngine.isValidAlertEvent(alertEventDO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetEnabledRuleCount_Success() {
        // 模拟统计启用规则数量
        when(rulesService.count(any(LambdaQueryWrapper.class))).thenReturn(2L);

        // 执行测试
        int result = alertRuleEngine.getEnabledRuleCount();

        // 验证结果
        assertEquals(2, result);
        verify(rulesService, times(1)).count(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetEnabledRuleCount_Exception() {
        // 模拟统计异常
        when(rulesService.count(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库异常"));

        // 执行测试
        int result = alertRuleEngine.getEnabledRuleCount();

        // 验证结果
        assertEquals(0, result);
    }

    @Test
    void testGetEnabledRules_Success() {
        // 模拟查询启用规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(enabledRules);

        // 执行测试
        List<RulesDO> result = alertRuleEngine.getEnabledRules();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetEnabledRules_Exception() {
        // 模拟查询异常
        when(rulesService.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库异常"));

        // 执行测试
        List<RulesDO> result = alertRuleEngine.getEnabledRules();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
