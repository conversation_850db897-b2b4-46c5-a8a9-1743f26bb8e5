package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.capital.CapitalService;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * CapitalMatcher单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class CapitalMatcherTest {

    @Mock
    private CapitalService capitalService;

    @InjectMocks
    private CapitalMatcher capitalMatcher;

    private Map<String, String> payloadMap;
    private RulesDO rulesDO;
    private CapitalDO capitalDO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        payloadMap = new HashMap<>();
        payloadMap.put("fund_provider_name", "测试资方");

        rulesDO = RulesDO.builder()
            .id(1L)
            .ruleName("测试规则")
            .ruleCode("TEST_RULE")
            .applyInvestor("1,2,3")
            .build();

        capitalDO = CapitalDO.builder()
            .id(1L)
            .capitalName("测试资方")
            .capitalCode("TEST_CAPITAL")
            .deleted(false)
            .build();
    }

    @Test
    void testMatchCapital_Success() {
        // 模拟资方查询成功
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertTrue(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_NoFundProviderName() {
        // 移除资方名称
        payloadMap.remove("fund_provider_name");

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, never()).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_EmptyFundProviderName() {
        // 设置空的资方名称
        payloadMap.put("fund_provider_name", "");

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, never()).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_CapitalNotFound() {
        // 模拟资方查询返回null
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_NoApplyInvestor() {
        // 设置规则没有适用资方
        rulesDO.setApplyInvestor(null);
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_EmptyApplyInvestor() {
        // 设置规则适用资方为空字符串
        rulesDO.setApplyInvestor("");
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_CapitalNotInRule() {
        // 设置资方ID不在规则适用范围内
        rulesDO.setApplyInvestor("2,3,4");
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_SingleCapitalInRule() {
        // 设置规则只包含一个资方ID
        rulesDO.setApplyInvestor("1");
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertTrue(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapital_DatabaseException() {
        // 模拟数据库查询异常
        when(capitalService.getOne(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        boolean result = capitalMatcher.matchCapital(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMatchCapitalWithDetail_Success() {
        // 模拟资方查询成功
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        CapitalMatcher.CapitalMatchResult result = capitalMatcher.matchCapitalWithDetail(payloadMap, rulesDO);

        // 验证结果
        assertTrue(result.isMatched());
        assertEquals(1L, result.getCapitalId());
        assertEquals("测试资方", result.getCapitalName());
        assertNotNull(result.getMessage());
        assertFalse(result.hasError());
    }

    @Test
    void testMatchCapitalWithDetail_Failure() {
        // 模拟资方查询返回null
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        CapitalMatcher.CapitalMatchResult result = capitalMatcher.matchCapitalWithDetail(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result.isMatched());
        assertNull(result.getCapitalId());
        assertNull(result.getCapitalName());
        assertNotNull(result.getMessage());
        assertFalse(result.hasError());
    }

    @Test
    void testMatchCapitalWithDetail_Error() {
        // 模拟数据库查询异常
        when(capitalService.getOne(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        CapitalMatcher.CapitalMatchResult result = capitalMatcher.matchCapitalWithDetail(payloadMap, rulesDO);

        // 验证结果
        assertFalse(result.isMatched());
        assertNull(result.getCapitalId());
        assertNull(result.getCapitalName());
        assertNull(result.getMessage());
        assertTrue(result.hasError());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testGetCapitalByName_Success() {
        // 模拟资方查询成功
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(capitalDO);

        // 执行测试
        CapitalDO result = capitalMatcher.getCapitalByName("测试资方");

        // 验证结果
        assertNotNull(result);
        assertEquals("测试资方", result.getCapitalName());
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetCapitalByName_NotFound() {
        // 模拟资方查询返回null
        when(capitalService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        CapitalDO result = capitalMatcher.getCapitalByName("不存在的资方");

        // 验证结果
        assertNull(result);
        verify(capitalService, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetCapitalByName_EmptyName() {
        // 执行测试
        CapitalDO result = capitalMatcher.getCapitalByName("");

        // 验证结果
        assertNull(result);
        verify(capitalService, never()).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testIsCapitalInRule_Success() {
        // 执行测试
        boolean result = capitalMatcher.isCapitalInRule(1L, "1,2,3");

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsCapitalInRule_NotInRule() {
        // 执行测试
        boolean result = capitalMatcher.isCapitalInRule(4L, "1,2,3");

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsCapitalInRule_NullCapitalId() {
        // 执行测试
        boolean result = capitalMatcher.isCapitalInRule(null, "1,2,3");

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsCapitalInRule_EmptyApplyInvestor() {
        // 执行测试
        boolean result = capitalMatcher.isCapitalInRule(1L, "");

        // 验证结果
        assertFalse(result);
    }
}
