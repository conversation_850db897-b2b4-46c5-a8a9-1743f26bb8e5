package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * AlertRuleEngine集成测试
 * 测试完整的规则评估流程
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class AlertRuleEngineIntegrationTest {

    @Mock
    private RulesService rulesService;

    @InjectMocks
    private AlertRuleEngine alertRuleEngine;

    private AlertEventDO alertEventDO;
    private List<RulesDO> mockRules;

    @BeforeEach
    void setUp() {
        // 创建测试用的预警事件
        alertEventDO = AlertEventDO.builder()
            .id(1L)
            .eventId("EVENT_001")
            .payload("{\"fund_provider_name\":\"测试资方\",\"amount\":\"1000\",\"status\":\"PENDING\"}")
            .build();

        // 创建测试用的规则
        mockRules = createMockRules();
    }

    @Test
    void testCompleteRuleEvaluationFlow_Success() {
        // 模拟查询启用的规则
        when(rulesService.list(any())).thenReturn(mockRules);

        // 执行规则评估
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(alertEventDO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isMatched());
        assertEquals(1L, result.getMatchedRuleId());
        assertEquals("高金额预警规则", result.getMatchedRuleName());
        assertEquals("HIGH_AMOUNT", result.getMatchedRuleCode());
        assertEquals("HIGH", result.getWarnLevel());

        // 验证处理详情
        assertNotNull(result.getProcessDetails());
        assertFalse(result.getProcessDetails().isEmpty());

        // 验证服务调用
        verify(rulesService, times(1)).list(any());
    }

    @Test
    void testCompleteRuleEvaluationFlow_NoMatch() {
        // 创建不匹配的事件
        AlertEventDO noMatchEvent = AlertEventDO.builder()
            .id(2L)
            .eventId("EVENT_002")
            .payload("{\"fund_provider_name\":\"其他资方\",\"amount\":\"100\",\"status\":\"COMPLETED\"}")
            .build();

        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行规则评估
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(noMatchEvent);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());

        // 验证处理详情
        assertNotNull(result.getProcessDetails());
        assertFalse(result.getProcessDetails().isEmpty());

        // 验证服务调用
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testMultipleRulesEvaluation() {
        // 创建能匹配多个规则的事件
        AlertEventDO multiMatchEvent = AlertEventDO.builder()
            .id(3L)
            .eventId("EVENT_003")
            .payload("{\"fund_provider_name\":\"测试资方\",\"amount\":\"5000\",\"status\":\"PENDING\"}")
            .build();

        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行规则评估
        RuleEvaluationResult result = alertRuleEngine.evaluateRules(multiMatchEvent);

        // 验证结果 - 应该匹配第一个符合条件的规则
        assertNotNull(result);
        assertTrue(result.isMatched());

        // 验证处理了多个规则
        assertNotNull(result.getProcessDetails());
        assertTrue(result.getProcessDetails().size() >= 2);

        // 验证服务调用
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testSpecificRuleEvaluation_Success() {
        // 模拟查询特定规则
        RulesDO specificRule = mockRules.get(0);
        when(rulesService.getById(1L)).thenReturn(specificRule);

        // 执行特定规则评估
        RuleEvaluationResult result = alertRuleEngine.evaluateRule(alertEventDO, 1L);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isMatched());
        assertEquals(1L, result.getMatchedRuleId());

        // 验证服务调用
        verify(rulesService, times(1)).getById(1L);
    }

    @Test
    void testSpecificRuleEvaluation_RuleNotFound() {
        // 模拟规则不存在
        when(rulesService.getById(999L)).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            alertRuleEngine.evaluateRule(alertEventDO, 999L);
        });

        assertTrue(exception.getMessage().contains("规则不存在"));

        // 验证服务调用
        verify(rulesService, times(1)).getById(999L);
    }

    @Test
    void testBatchRuleEvaluation_Success() {
        // 创建批量事件
        List<AlertEventDO> events = Arrays.asList(
            alertEventDO,
            AlertEventDO.builder()
                .id(2L)
                .eventId("EVENT_002")
                .payload("{\"fund_provider_name\":\"测试资方\",\"amount\":\"2000\",\"status\":\"PENDING\"}")
                .build(),
            AlertEventDO.builder()
                .id(3L)
                .eventId("EVENT_003")
                .payload("{\"fund_provider_name\":\"其他资方\",\"amount\":\"500\",\"status\":\"COMPLETED\"}")
                .build()
        );

        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行批量评估
        List<RuleEvaluationResult> results = alertRuleEngine.batchEvaluateRules(events);

        // 验证结果
        assertNotNull(results);
        assertEquals(3, results.size());

        // 验证每个结果都不为null
        for (RuleEvaluationResult result : results) {
            assertNotNull(result);
        }

        // 验证服务调用次数
        verify(rulesService, times(3)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testBatchRuleEvaluation_EmptyList() {
        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            alertRuleEngine.batchEvaluateRules(new ArrayList<>());
        });

        assertTrue(exception.getMessage().contains("预警事件列表不能为空"));
    }

    @Test
    void testGetEnabledRuleCount() {
        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行测试
        int count = alertRuleEngine.getEnabledRuleCount();

        // 验证结果
        assertEquals(3, count);

        // 验证服务调用
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetEnabledRules() {
        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行测试
        List<RulesDO> rules = alertRuleEngine.getEnabledRules();

        // 验证结果
        assertNotNull(rules);
        assertEquals(3, rules.size());

        // 验证服务调用
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetEvaluationStatistics() {
        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行测试
        Map<String, Object> statistics = alertRuleEngine.getEvaluationStatistics();

        // 验证结果
        assertNotNull(statistics);
        assertTrue(statistics.containsKey("enabledRuleCount"));
        assertTrue(statistics.containsKey("systemUptime"));
        assertTrue(statistics.containsKey("totalMemory"));
        assertTrue(statistics.containsKey("freeMemory"));
        assertTrue(statistics.containsKey("usedMemory"));

        // 验证启用规则数量
        assertEquals(3, statistics.get("enabledRuleCount"));
    }

    @Test
    void testIsValidAlertEvent() {
        // 测试有效事件
        assertTrue(alertRuleEngine.isValidAlertEvent(alertEventDO));

        // 测试null事件
        assertFalse(alertRuleEngine.isValidAlertEvent(null));

        // 测试ID为null的事件
        AlertEventDO invalidEvent1 = AlertEventDO.builder()
            .id(null)
            .eventId("EVENT_001")
            .payload("{\"test\":\"data\"}")
            .build();
        assertFalse(alertRuleEngine.isValidAlertEvent(invalidEvent1));

        // 测试payload为空的事件
        AlertEventDO invalidEvent2 = AlertEventDO.builder()
            .id(1L)
            .eventId("EVENT_001")
            .payload("")
            .build();
        assertFalse(alertRuleEngine.isValidAlertEvent(invalidEvent2));
    }

    @Test
    void testClearCache() {
        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            alertRuleEngine.clearCache();
        });
    }

    @Test
    void testWarmUp() {
        // 模拟查询启用的规则
        when(rulesService.list(any(LambdaQueryWrapper.class))).thenReturn(mockRules);

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            alertRuleEngine.warmUp();
        });

        // 验证服务调用
        verify(rulesService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    /**
     * 创建模拟规则数据
     */
    private List<RulesDO> createMockRules() {
        List<RulesDO> rules = new ArrayList<>();

        // 规则1：高金额预警规则
        RulesDO rule1 = new RulesDO();
        rule1.setId(1L);
        rule1.setRuleName("高金额预警规则");
        rule1.setRuleCode("HIGH_AMOUNT");
        rule1.setRuleStatus("0"); // 启用
        rule1.setRuleMatching("1"); // 满足所有条件
        rule1.setApplyInvestor("测试资方,其他资方");
        rule1.setDeleted(false);
        rules.add(rule1);

        // 规则2：状态预警规则
        RulesDO rule2 = new RulesDO();
        rule2.setId(2L);
        rule2.setRuleName("状态预警规则");
        rule2.setRuleCode("STATUS_CHECK");
        rule2.setRuleStatus("0"); // 启用
        rule2.setRuleMatching("0"); // 满足任一条件
        rule2.setApplyInvestor("测试资方");
        rule2.setDeleted(false);
        rules.add(rule2);

        // 规则3：综合预警规则
        RulesDO rule3 = new RulesDO();
        rule3.setId(3L);
        rule3.setRuleName("综合预警规则");
        rule3.setRuleCode("COMPREHENSIVE");
        rule3.setRuleStatus("0"); // 启用
        rule3.setRuleMatching("1"); // 满足所有条件
        rule3.setApplyInvestor("测试资方");
        rule3.setDeleted(false);
        rules.add(rule3);

        return rules;
    }
}
