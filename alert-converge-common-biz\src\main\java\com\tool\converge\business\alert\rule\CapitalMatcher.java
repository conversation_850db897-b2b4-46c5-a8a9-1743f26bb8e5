package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.capital.CapitalService;
import com.tool.converge.repository.domain.capital.db.CapitalDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 资方匹配器
 * 用于验证事件的资方是否在规则配置的适用范围内
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class CapitalMatcher {

    @Resource
    private CapitalService capitalService;

    /**
     * 验证资方是否匹配规则
     * 
     * @param payloadMap 事件数据映射
     * @param rulesDO 规则配置
     * @return 是否匹配
     */
    public boolean matchCapital(Map<String, String> payloadMap, RulesDO rulesDO) {
        try {
            // 1. 从payload中获取资方名称
            String fundProviderName = payloadMap.get("fund_provider_name");
            if (StringUtils.isBlank(fundProviderName)) {
                log.debug("资方匹配失败：payload中未找到fund_provider_name字段，规则ID：{}", rulesDO.getId());
                return false;
            }

            // 2. 根据资方名称查询资方ID
            CapitalDO capital = capitalService.getOne(
                new LambdaQueryWrapper<CapitalDO>()
                    .eq(CapitalDO::getCapitalName, fundProviderName)
                    .eq(CapitalDO::getDeleted, false)
            );

            if (capital == null) {
                log.debug("资方匹配失败：未找到资方信息，资方名称：{}，规则ID：{}", fundProviderName, rulesDO.getId());
                return false;
            }

            // 3. 检查规则的适用资方列表
            String applyInvestor = rulesDO.getApplyInvestor();
            if (StringUtils.isBlank(applyInvestor)) {
                log.debug("资方匹配失败：规则未配置适用资方，规则ID：{}", rulesDO.getId());
                return false;
            }

            // 4. 解析逗号分隔的资方ID列表
            List<String> capitalIds = Arrays.asList(applyInvestor.split(","));
            boolean matched = capitalIds.contains(capital.getId().toString());

            if (matched) {
                log.debug("资方匹配成功：资方名称：{}，资方ID：{}，规则ID：{}", 
                    fundProviderName, capital.getId(), rulesDO.getId());
            } else {
                log.debug("资方匹配失败：资方不在规则适用范围内，资方名称：{}，资方ID：{}，规则适用资方：{}，规则ID：{}", 
                    fundProviderName, capital.getId(), applyInvestor, rulesDO.getId());
            }

            return matched;

        } catch (Exception e) {
            log.error("资方匹配异常，规则ID：{}，错误：{}", rulesDO.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证资方是否匹配规则（带详细结果）
     * 
     * @param payloadMap 事件数据映射
     * @param rulesDO 规则配置
     * @return 匹配结果详情
     */
    public CapitalMatchResult matchCapitalWithDetail(Map<String, String> payloadMap, RulesDO rulesDO) {
        try {
            // 1. 从payload中获取资方名称
            String fundProviderName = payloadMap.get("fund_provider_name");
            if (StringUtils.isBlank(fundProviderName)) {
                return CapitalMatchResult.failure("payload中未找到fund_provider_name字段");
            }

            // 2. 根据资方名称查询资方ID
            CapitalDO capital = capitalService.getOne(
                new LambdaQueryWrapper<CapitalDO>()
                    .eq(CapitalDO::getCapitalName, fundProviderName)
                    .eq(CapitalDO::getDeleted, false)
            );

            if (capital == null) {
                return CapitalMatchResult.failure("未找到资方信息，资方名称：" + fundProviderName);
            }

            // 3. 检查规则的适用资方列表
            String applyInvestor = rulesDO.getApplyInvestor();
            if (StringUtils.isBlank(applyInvestor)) {
                return CapitalMatchResult.failure("规则未配置适用资方");
            }

            // 4. 解析逗号分隔的资方ID列表
            List<String> capitalIds = Arrays.asList(applyInvestor.split(","));
            boolean matched = capitalIds.contains(capital.getId().toString());

            if (matched) {
                return CapitalMatchResult.success(capital.getId(), fundProviderName, 
                    "资方匹配成功");
            } else {
                return CapitalMatchResult.failure(
                    String.format("资方不在规则适用范围内，资方ID：%d，规则适用资方：%s", 
                        capital.getId(), applyInvestor));
            }

        } catch (Exception e) {
            log.error("资方匹配异常，规则ID：{}，错误：{}", rulesDO.getId(), e.getMessage(), e);
            return CapitalMatchResult.error("资方匹配异常：" + e.getMessage());
        }
    }

    /**
     * 根据资方名称获取资方信息
     * 
     * @param capitalName 资方名称
     * @return 资方信息，如果不存在则返回null
     */
    public CapitalDO getCapitalByName(String capitalName) {
        if (StringUtils.isBlank(capitalName)) {
            return null;
        }

        try {
            return capitalService.getOne(
                new LambdaQueryWrapper<CapitalDO>()
                    .eq(CapitalDO::getCapitalName, capitalName)
                    .eq(CapitalDO::getDeleted, false)
            );
        } catch (Exception e) {
            log.error("查询资方信息异常，资方名称：{}，错误：{}", capitalName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查资方ID是否在规则适用范围内
     * 
     * @param capitalId 资方ID
     * @param applyInvestor 规则适用资方（逗号分隔的ID列表）
     * @return 是否在适用范围内
     */
    public boolean isCapitalInRule(Long capitalId, String applyInvestor) {
        if (capitalId == null || StringUtils.isBlank(applyInvestor)) {
            return false;
        }

        try {
            List<String> capitalIds = Arrays.asList(applyInvestor.split(","));
            return capitalIds.contains(capitalId.toString());
        } catch (Exception e) {
            log.error("检查资方适用范围异常，资方ID：{}，适用资方：{}，错误：{}", 
                capitalId, applyInvestor, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 资方匹配结果
     */
    public static class CapitalMatchResult {
        private boolean matched;
        private Long capitalId;
        private String capitalName;
        private String message;
        private String errorMessage;

        private CapitalMatchResult(boolean matched, Long capitalId, String capitalName, 
                                 String message, String errorMessage) {
            this.matched = matched;
            this.capitalId = capitalId;
            this.capitalName = capitalName;
            this.message = message;
            this.errorMessage = errorMessage;
        }

        public static CapitalMatchResult success(Long capitalId, String capitalName, String message) {
            return new CapitalMatchResult(true, capitalId, capitalName, message, null);
        }

        public static CapitalMatchResult failure(String message) {
            return new CapitalMatchResult(false, null, null, message, null);
        }

        public static CapitalMatchResult error(String errorMessage) {
            return new CapitalMatchResult(false, null, null, null, errorMessage);
        }

        // Getters
        public boolean isMatched() { return matched; }
        public Long getCapitalId() { return capitalId; }
        public String getCapitalName() { return capitalName; }
        public String getMessage() { return message; }
        public String getErrorMessage() { return errorMessage; }
        public boolean hasError() { return errorMessage != null; }
    }
}
