package com.tool.converge.business.alert.rule.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 规则评估结果
 * 用于封装预警规则判断的结果信息
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleEvaluationResult {

    /**
     * 是否匹配成功
     */
    private boolean matched;

    /**
     * 匹配的规则ID
     */
    private Long matchedRuleId;

    /**
     * 匹配的规则名称
     */
    private String matchedRuleName;

    /**
     * 匹配的规则编码
     */
    private String matchedRuleCode;

    /**
     * 预警级别
     */
    private String warnLevel;

    /**
     * 匹配失败原因
     */
    private String failureReason;

    /**
     * 处理详情列表
     */
    @Builder.Default
    private List<RuleProcessDetail> processDetails = new ArrayList<>();

    /**
     * 评估开始时间（毫秒时间戳）
     */
    private Long evaluationStartTime;

    /**
     * 评估结束时间（毫秒时间戳）
     */
    private Long evaluationEndTime;

    /**
     * 评估耗时（毫秒）
     */
    public Long getEvaluationDuration() {
        if (evaluationStartTime != null && evaluationEndTime != null) {
            return evaluationEndTime - evaluationStartTime;
        }
        return null;
    }

    /**
     * 添加处理详情
     * 
     * @param detail 处理详情
     */
    public void addProcessDetail(RuleProcessDetail detail) {
        if (this.processDetails == null) {
            this.processDetails = new ArrayList<>();
        }
        this.processDetails.add(detail);
    }

    /**
     * 获取匹配成功的处理详情数量
     * 
     * @return 匹配成功的数量
     */
    public int getMatchedCount() {
        if (processDetails == null || processDetails.isEmpty()) {
            return 0;
        }
        return (int) processDetails.stream()
            .filter(detail -> detail.getStatus() == ProcessStatus.SUCCESS)
            .count();
    }

    /**
     * 获取处理失败的详情数量
     * 
     * @return 处理失败的数量
     */
    public int getFailedCount() {
        if (processDetails == null || processDetails.isEmpty()) {
            return 0;
        }
        return (int) processDetails.stream()
            .filter(detail -> detail.getStatus() == ProcessStatus.FAILED)
            .count();
    }

    /**
     * 获取跳过的详情数量
     * 
     * @return 跳过的数量
     */
    public int getSkippedCount() {
        if (processDetails == null || processDetails.isEmpty()) {
            return 0;
        }
        return (int) processDetails.stream()
            .filter(detail -> detail.getStatus() == ProcessStatus.SKIPPED)
            .count();
    }

    /**
     * 检查是否有处理异常
     * 
     * @return 是否有异常
     */
    public boolean hasError() {
        if (processDetails == null || processDetails.isEmpty()) {
            return false;
        }
        return processDetails.stream()
            .anyMatch(detail -> detail.getStatus() == ProcessStatus.ERROR);
    }

    /**
     * 获取第一个匹配成功的规则详情
     * 
     * @return 第一个成功的规则详情，如果没有则返回null
     */
    public RuleProcessDetail getFirstMatchedRule() {
        if (processDetails == null || processDetails.isEmpty()) {
            return null;
        }
        return processDetails.stream()
            .filter(detail -> detail.getStatus() == ProcessStatus.SUCCESS)
            .findFirst()
            .orElse(null);
    }

    /**
     * 创建匹配成功的结果
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param ruleCode 规则编码
     * @param warnLevel 预警级别
     * @return 评估结果
     */
    public static RuleEvaluationResult success(Long ruleId, String ruleName, String ruleCode, String warnLevel) {
        return RuleEvaluationResult.builder()
            .matched(true)
            .matchedRuleId(ruleId)
            .matchedRuleName(ruleName)
            .matchedRuleCode(ruleCode)
            .warnLevel(warnLevel)
            .build();
    }

    /**
     * 创建匹配失败的结果
     * 
     * @param reason 失败原因
     * @return 评估结果
     */
    public static RuleEvaluationResult failure(String reason) {
        return RuleEvaluationResult.builder()
            .matched(false)
            .failureReason(reason)
            .build();
    }

    /**
     * 创建异常的结果
     * 
     * @param reason 异常原因
     * @return 评估结果
     */
    public static RuleEvaluationResult error(String reason) {
        return RuleEvaluationResult.builder()
            .matched(false)
            .failureReason(reason)
            .build();
    }
}
