package com.tool.converge.business.alert.rule.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 条件评估详情
 * 用于记录单个条件的评估过程和结果
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionEvaluationDetail {

    /**
     * 条件ID
     */
    private Long conditionId;

    /**
     * 设置项（从payload中查找的字段名）
     */
    private String settingItem;

    /**
     * 运算符
     */
    private String operator;

    /**
     * 期望值（配置的比较值）
     */
    private String expectedValue;

    /**
     * 实际值（从payload中获取的值）
     */
    private String actualValue;

    /**
     * 评估结果
     */
    private boolean result;

    /**
     * 评估消息
     */
    private String message;

    /**
     * 评估开始时间（毫秒时间戳）
     */
    private Long evaluationStartTime;

    /**
     * 评估结束时间（毫秒时间戳）
     */
    private Long evaluationEndTime;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 是否发生类型转换
     */
    private boolean typeConverted;

    /**
     * 错误信息（如果评估过程中发生错误）
     */
    private String errorMessage;

    /**
     * 获取评估耗时（毫秒）
     * 
     * @return 评估耗时
     */
    public Long getEvaluationDuration() {
        if (evaluationStartTime != null && evaluationEndTime != null) {
            return evaluationEndTime - evaluationStartTime;
        }
        return null;
    }

    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 创建成功的评估详情
     * 
     * @param conditionId 条件ID
     * @param settingItem 设置项
     * @param operator 运算符
     * @param expectedValue 期望值
     * @param actualValue 实际值
     * @param result 评估结果
     * @return 条件评估详情
     */
    public static ConditionEvaluationDetail success(Long conditionId, String settingItem, 
                                                   String operator, String expectedValue, 
                                                   String actualValue, boolean result) {
        return ConditionEvaluationDetail.builder()
            .conditionId(conditionId)
            .settingItem(settingItem)
            .operator(operator)
            .expectedValue(expectedValue)
            .actualValue(actualValue)
            .result(result)
            .message(result ? "条件匹配成功" : "条件匹配失败")
            .build();
    }

    /**
     * 创建失败的评估详情
     * 
     * @param conditionId 条件ID
     * @param settingItem 设置项
     * @param operator 运算符
     * @param expectedValue 期望值
     * @param actualValue 实际值
     * @param errorMessage 错误信息
     * @return 条件评估详情
     */
    public static ConditionEvaluationDetail failure(Long conditionId, String settingItem, 
                                                   String operator, String expectedValue, 
                                                   String actualValue, String errorMessage) {
        return ConditionEvaluationDetail.builder()
            .conditionId(conditionId)
            .settingItem(settingItem)
            .operator(operator)
            .expectedValue(expectedValue)
            .actualValue(actualValue)
            .result(false)
            .message("条件评估失败")
            .errorMessage(errorMessage)
            .build();
    }

    /**
     * 创建跳过的评估详情
     * 
     * @param conditionId 条件ID
     * @param settingItem 设置项
     * @param reason 跳过原因
     * @return 条件评估详情
     */
    public static ConditionEvaluationDetail skipped(Long conditionId, String settingItem, String reason) {
        return ConditionEvaluationDetail.builder()
            .conditionId(conditionId)
            .settingItem(settingItem)
            .result(false)
            .message("条件评估跳过: " + reason)
            .build();
    }
}
