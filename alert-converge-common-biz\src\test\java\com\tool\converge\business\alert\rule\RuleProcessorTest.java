package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.alert.rule.model.ConditionEvaluationDetail;
import com.tool.converge.business.alert.rule.model.ProcessStatus;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.WarnConditionService;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * RuleProcessor单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class RuleProcessorTest {

    @Mock
    private WarnConditionService warnConditionService;

    @Mock
    private ConditionEvaluator conditionEvaluator;

    @InjectMocks
    private RuleProcessor ruleProcessor;

    private Map<String, String> payloadMap;
    private RulesDO rulesDO;
    private List<WarnConditionDO> conditions;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        payloadMap = new HashMap<>();
        payloadMap.put("amount", "100.50");
        payloadMap.put("status", "ACTIVE");

        rulesDO = RulesDO.builder()
            .id(1L)
            .ruleName("测试规则")
            .ruleCode("TEST_RULE")
            .ruleMatching("1") // 满足所有条件
            .build();

        conditions = new ArrayList<>();
        conditions.add(WarnConditionDO.builder()
            .id(1L)
            .rulesId(1L)
            .settingItem("amount")
            .operator("GT")
            .assignmentItem(new BigDecimal("50.00"))
            .build());
        conditions.add(WarnConditionDO.builder()
            .id(2L)
            .rulesId(1L)
            .settingItem("status")
            .operator("EQ")
            .assignmentItem(new BigDecimal("1"))
            .build());
    }

    @Test
    void testProcessRule_AllMatch_Success() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟条件评估都成功
        when(conditionEvaluator.evaluateCondition(any(WarnConditionDO.class), any(Map.class)))
            .thenReturn(true);

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertTrue(result);
        verify(warnConditionService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(conditionEvaluator, times(2)).evaluateCondition(any(WarnConditionDO.class), any(Map.class));
    }

    @Test
    void testProcessRule_AllMatch_Failure() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟第一个条件成功，第二个条件失败
        when(conditionEvaluator.evaluateCondition(conditions.get(0), payloadMap)).thenReturn(true);
        when(conditionEvaluator.evaluateCondition(conditions.get(1), payloadMap)).thenReturn(false);

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertFalse(result);
        verify(conditionEvaluator, times(2)).evaluateCondition(any(WarnConditionDO.class), any(Map.class));
    }

    @Test
    void testProcessRule_AnyMatch_Success() {
        // 修改为满足任一条件模式
        rulesDO.setRuleMatching("0");
        
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟第一个条件成功
        when(conditionEvaluator.evaluateCondition(conditions.get(0), payloadMap)).thenReturn(true);

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertTrue(result);
        // 在满足任一条件模式下，第一个条件成功后应该立即返回，不会评估第二个条件
        verify(conditionEvaluator, times(1)).evaluateCondition(any(WarnConditionDO.class), any(Map.class));
    }

    @Test
    void testProcessRule_AnyMatch_Failure() {
        // 修改为满足任一条件模式
        rulesDO.setRuleMatching("0");
        
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟所有条件都失败
        when(conditionEvaluator.evaluateCondition(any(WarnConditionDO.class), any(Map.class)))
            .thenReturn(false);

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertFalse(result);
        verify(conditionEvaluator, times(2)).evaluateCondition(any(WarnConditionDO.class), any(Map.class));
    }

    @Test
    void testProcessRule_NoConditions() {
        // 模拟查询条件返回空列表
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertFalse(result);
        verify(warnConditionService, times(1)).list(any(LambdaQueryWrapper.class));
        verify(conditionEvaluator, never()).evaluateCondition(any(WarnConditionDO.class), any(Map.class));
    }

    @Test
    void testProcessRule_DatabaseException() {
        // 模拟数据库查询异常
        when(warnConditionService.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        boolean result = ruleProcessor.processRule(rulesDO, payloadMap);

        // 验证结果
        assertFalse(result);
        verify(warnConditionService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testProcessRuleWithDetail_AllMatch_Success() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟条件评估详情都成功
        ConditionEvaluationDetail detail1 = ConditionEvaluationDetail.success(1L, "amount", "GT", "50.00", "100.50", true);
        ConditionEvaluationDetail detail2 = ConditionEvaluationDetail.success(2L, "status", "EQ", "1", "ACTIVE", true);
        
        when(conditionEvaluator.evaluateConditionWithDetail(conditions.get(0), payloadMap)).thenReturn(detail1);
        when(conditionEvaluator.evaluateConditionWithDetail(conditions.get(1), payloadMap)).thenReturn(detail2);

        // 执行测试
        RuleProcessDetail result = ruleProcessor.processRuleWithDetail(rulesDO, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(ProcessStatus.SUCCESS, result.getStatus());
        assertEquals(1L, result.getRuleId());
        assertEquals("测试规则", result.getRuleName());
        assertEquals("TEST_RULE", result.getRuleCode());
        assertEquals("1", result.getRuleMatching());
        assertEquals(2, result.getTotalConditionCount());
        assertEquals(2, result.getMatchedConditionCount());
        assertEquals(2, result.getConditionDetails().size());
        assertNotNull(result.getProcessStartTime());
        assertNotNull(result.getProcessEndTime());
        assertNotNull(result.getProcessDuration());
    }

    @Test
    void testProcessRuleWithDetail_AllMatch_Failure() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟第一个条件成功，第二个条件失败
        ConditionEvaluationDetail detail1 = ConditionEvaluationDetail.success(1L, "amount", "GT", "50.00", "100.50", true);
        ConditionEvaluationDetail detail2 = ConditionEvaluationDetail.success(2L, "status", "EQ", "1", "ACTIVE", false);
        
        when(conditionEvaluator.evaluateConditionWithDetail(conditions.get(0), payloadMap)).thenReturn(detail1);
        when(conditionEvaluator.evaluateConditionWithDetail(conditions.get(1), payloadMap)).thenReturn(detail2);

        // 执行测试
        RuleProcessDetail result = ruleProcessor.processRuleWithDetail(rulesDO, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(ProcessStatus.FAILED, result.getStatus());
        assertEquals(1, result.getMatchedConditionCount());
        assertEquals(2, result.getConditionDetails().size());
        assertTrue(result.getMessage().contains("条件不满足"));
    }

    @Test
    void testProcessRuleWithDetail_AnyMatch_Success() {
        // 修改为满足任一条件模式
        rulesDO.setRuleMatching("0");
        
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);
        
        // 模拟第一个条件成功
        ConditionEvaluationDetail detail1 = ConditionEvaluationDetail.success(1L, "amount", "GT", "50.00", "100.50", true);
        when(conditionEvaluator.evaluateConditionWithDetail(conditions.get(0), payloadMap)).thenReturn(detail1);

        // 执行测试
        RuleProcessDetail result = ruleProcessor.processRuleWithDetail(rulesDO, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(ProcessStatus.SUCCESS, result.getStatus());
        assertEquals("0", result.getRuleMatching());
        assertEquals(1, result.getMatchedConditionCount());
        assertEquals(1, result.getConditionDetails().size()); // 只评估了第一个条件
        assertTrue(result.getMessage().contains("满足任一条件模式"));
    }

    @Test
    void testProcessRuleWithDetail_NoConditions() {
        // 模拟查询条件返回空列表
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // 执行测试
        RuleProcessDetail result = ruleProcessor.processRuleWithDetail(rulesDO, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(ProcessStatus.FAILED, result.getStatus());
        assertEquals(0, result.getTotalConditionCount());
        assertEquals(0, result.getMatchedConditionCount());
        assertTrue(result.getMessage().contains("规则无条件配置"));
    }

    @Test
    void testProcessRuleWithDetail_Exception() {
        // 模拟数据库查询异常
        when(warnConditionService.list(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        RuleProcessDetail result = ruleProcessor.processRuleWithDetail(rulesDO, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(ProcessStatus.ERROR, result.getStatus());
        assertTrue(result.getMessage().contains("规则处理异常"));
        assertNotNull(result.getErrorMessage());
        assertTrue(result.hasError());
    }

    @Test
    void testGetRuleConditions_Success() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);

        // 执行测试
        List<WarnConditionDO> result = ruleProcessor.getRuleConditions(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(warnConditionService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetRuleConditions_NullRuleId() {
        // 执行测试
        List<WarnConditionDO> result = ruleProcessor.getRuleConditions(null);

        // 验证结果
        assertNull(result);
        verify(warnConditionService, never()).list(any(LambdaQueryWrapper.class));
    }

    @Test
    void testIsValidRule_Valid() {
        // 模拟查询条件
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(conditions);

        // 执行测试
        boolean result = ruleProcessor.isValidRule(rulesDO);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsValidRule_NullRule() {
        // 执行测试
        boolean result = ruleProcessor.isValidRule(null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidRule_InvalidRuleMatching() {
        // 设置无效的规则匹配模式
        rulesDO.setRuleMatching("2");

        // 执行测试
        boolean result = ruleProcessor.isValidRule(rulesDO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidRule_NoConditions() {
        // 模拟查询条件返回空列表
        when(warnConditionService.list(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // 执行测试
        boolean result = ruleProcessor.isValidRule(rulesDO);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetRuleMatchingDescription() {
        // 测试满足所有条件
        assertEquals("满足所有条件", ruleProcessor.getRuleMatchingDescription("1"));
        
        // 测试满足任一条件
        assertEquals("满足任一条件", ruleProcessor.getRuleMatchingDescription("0"));
        
        // 测试未知模式
        assertEquals("未知模式", ruleProcessor.getRuleMatchingDescription("2"));
        assertEquals("未知模式", ruleProcessor.getRuleMatchingDescription(null));
    }

    @Test
    void testCountRuleConditions_Success() {
        // 模拟统计条件数量
        when(warnConditionService.count(any(LambdaQueryWrapper.class))).thenReturn(2L);

        // 执行测试
        int result = ruleProcessor.countRuleConditions(1L);

        // 验证结果
        assertEquals(2, result);
        verify(warnConditionService, times(1)).count(any(LambdaQueryWrapper.class));
    }

    @Test
    void testCountRuleConditions_NullRuleId() {
        // 执行测试
        int result = ruleProcessor.countRuleConditions(null);

        // 验证结果
        assertEquals(0, result);
        verify(warnConditionService, never()).count(any(LambdaQueryWrapper.class));
    }
}
