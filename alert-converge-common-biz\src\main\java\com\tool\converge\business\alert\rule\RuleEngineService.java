package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;

import java.util.List;

/**
 * 规则引擎服务接口
 * 用于评估预警事件是否匹配配置的规则
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface RuleEngineService {

    /**
     * 评估预警事件是否匹配规则
     * 
     * <p>该方法会：</p>
     * <ul>
     *   <li>解析预警事件的payload数据</li>
     *   <li>查询所有启用的规则配置</li>
     *   <li>对每个规则进行资方匹配验证</li>
     *   <li>对匹配的规则进行条件评估</li>
     *   <li>返回综合的评估结果</li>
     * </ul>
     * 
     * <p>处理流程：</p>
     * <ol>
     *   <li>验证预警事件数据的完整性</li>
     *   <li>解析payload JSON数据为键值对映射</li>
     *   <li>查询所有启用状态的规则</li>
     *   <li>逐个评估规则：
     *     <ul>
     *       <li>验证资方是否在规则适用范围内</li>
     *       <li>如果资方匹配，则评估规则的所有条件</li>
     *       <li>根据规则匹配模式（满足所有条件/满足任一条件）判断是否匹配</li>
     *     </ul>
     *   </li>
     *   <li>构建并返回评估结果</li>
     * </ol>
     * 
     * @param alertEventDO 预警事件，包含事件ID、payload等信息
     * @return 规则评估结果，包含是否匹配、匹配的规则信息、处理详情等
     * 
     * @throws IllegalArgumentException 当预警事件为null或数据不完整时
     * @throws RuntimeException 当发生系统异常时（如数据库连接异常、JSON解析异常等）
     * 
     * @see RuleEvaluationResult
     * @see AlertEventDO
     */
    RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO);

    /**
     * 评估指定规则是否匹配预警事件
     * 
     * <p>该方法用于评估单个指定的规则，适用于以下场景：</p>
     * <ul>
     *   <li>测试特定规则的匹配效果</li>
     *   <li>调试规则配置问题</li>
     *   <li>手动触发特定规则的评估</li>
     * </ul>
     * 
     * @param alertEventDO 预警事件
     * @param ruleId 规则ID
     * @return 规则评估结果
     * 
     * @throws IllegalArgumentException 当预警事件为null、规则ID为null或规则不存在时
     * @throws RuntimeException 当发生系统异常时
     */
    RuleEvaluationResult evaluateRule(AlertEventDO alertEventDO, Long ruleId);

    /**
     * 检查预警事件数据的完整性
     * 
     * <p>验证预警事件是否包含规则评估所需的基本信息：</p>
     * <ul>
     *   <li>事件ID不能为null</li>
     *   <li>payload不能为空</li>
     *   <li>其他必要字段的完整性</li>
     * </ul>
     * 
     * @param alertEventDO 预警事件
     * @return 是否有效，true表示数据完整，false表示数据不完整
     */
    boolean isValidAlertEvent(AlertEventDO alertEventDO);

    /**
     * 获取启用的规则数量
     * 
     * <p>统计当前系统中启用状态的规则总数，用于：</p>
     * <ul>
     *   <li>监控规则配置情况</li>
     *   <li>性能评估和容量规划</li>
     *   <li>系统健康检查</li>
     * </ul>
     * 
     * @return 启用的规则数量，如果查询异常则返回0
     */
    int getEnabledRuleCount();

    /**
     * 获取所有启用的规则
     * 
     * <p>查询所有启用状态的规则配置，用于：</p>
     * <ul>
     *   <li>规则管理和维护</li>
     *   <li>批量操作和分析</li>
     *   <li>系统监控和报告</li>
     * </ul>
     * 
     * @return 启用的规则列表，如果查询异常则返回空列表
     */
    List<RulesDO> getEnabledRules();

    /**
     * 批量评估预警事件
     * 
     * <p>对多个预警事件进行批量规则评估，适用于：</p>
     * <ul>
     *   <li>批量处理历史数据</li>
     *   <li>性能测试和压力测试</li>
     *   <li>数据迁移和同步</li>
     * </ul>
     * 
     * @param alertEvents 预警事件列表
     * @return 评估结果列表，与输入事件列表一一对应
     * 
     * @throws IllegalArgumentException 当事件列表为null或为空时
     */
    List<RuleEvaluationResult> batchEvaluateRules(List<AlertEventDO> alertEvents);

    /**
     * 获取规则评估统计信息
     * 
     * <p>提供规则评估的统计数据，包括：</p>
     * <ul>
     *   <li>总评估次数</li>
     *   <li>成功匹配次数</li>
     *   <li>失败次数</li>
     *   <li>平均处理时间</li>
     * </ul>
     * 
     * @return 统计信息映射，键为统计项名称，值为统计数据
     */
    java.util.Map<String, Object> getEvaluationStatistics();

    /**
     * 清理规则评估缓存
     * 
     * <p>清理规则引擎相关的缓存数据，包括：</p>
     * <ul>
     *   <li>规则配置缓存</li>
     *   <li>资方信息缓存</li>
     *   <li>条件配置缓存</li>
     * </ul>
     * 
     * <p>适用场景：</p>
     * <ul>
     *   <li>规则配置更新后</li>
     *   <li>系统维护期间</li>
     *   <li>内存清理需求</li>
     * </ul>
     */
    void clearCache();

    /**
     * 预热规则引擎
     * 
     * <p>预加载规则引擎所需的数据和配置，包括：</p>
     * <ul>
     *   <li>加载所有启用的规则配置</li>
     *   <li>预加载资方信息</li>
     *   <li>初始化运算符映射</li>
     * </ul>
     * 
     * <p>建议在系统启动时调用，以提高首次评估的性能。</p>
     */
    void warmUp();
}
