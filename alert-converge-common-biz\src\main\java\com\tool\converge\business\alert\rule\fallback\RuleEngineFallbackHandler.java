package com.tool.converge.business.alert.rule.fallback;

import com.tool.converge.business.alert.rule.exception.RuleEngineException;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎降级处理器
 * 用于处理规则引擎异常情况下的降级策略
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class RuleEngineFallbackHandler {

    /**
     * 降级策略配置
     */
    private final Map<String, FallbackStrategy> fallbackStrategies = new HashMap<>();

    /**
     * 降级策略枚举
     */
    public enum FallbackStrategy {
        /**
         * 返回失败结果
         */
        RETURN_FAILURE,
        
        /**
         * 返回默认成功结果
         */
        RETURN_DEFAULT_SUCCESS,
        
        /**
         * 抛出异常
         */
        THROW_EXCEPTION,
        
        /**
         * 记录日志并返回失败
         */
        LOG_AND_FAIL
    }

    public RuleEngineFallbackHandler() {
        // 初始化默认降级策略
        initDefaultFallbackStrategies();
    }

    /**
     * 初始化默认降级策略
     */
    private void initDefaultFallbackStrategies() {
        fallbackStrategies.put("PARSE_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("DATABASE_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("RULE_CONFIG_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("CONDITION_EVAL_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("CAPITAL_MATCH_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("SYSTEM_ERROR", FallbackStrategy.LOG_AND_FAIL);
        fallbackStrategies.put("VALIDATION_ERROR", FallbackStrategy.THROW_EXCEPTION);
        fallbackStrategies.put("DEFAULT", FallbackStrategy.LOG_AND_FAIL);
    }

    /**
     * 处理规则评估异常
     * 
     * @param alertEventDO 预警事件
     * @param exception 异常信息
     * @return 降级处理结果
     */
    public RuleEvaluationResult handleEvaluationException(AlertEventDO alertEventDO, Exception exception) {
        try {
            String errorCode = extractErrorCode(exception);
            FallbackStrategy strategy = getFallbackStrategy(errorCode);
            
            log.warn("规则评估异常，启用降级策略，事件ID：{}，异常类型：{}，策略：{}", 
                alertEventDO != null ? alertEventDO.getId() : "unknown", errorCode, strategy);

            switch (strategy) {
                case RETURN_FAILURE:
                    return createFailureResult(exception);
                    
                case RETURN_DEFAULT_SUCCESS:
                    return createDefaultSuccessResult();
                    
                case THROW_EXCEPTION:
                    if (exception instanceof RuntimeException) {
                        throw (RuntimeException) exception;
                    } else {
                        throw new RuntimeException("规则评估异常", exception);
                    }

                case LOG_AND_FAIL:
                default:
                    logException(alertEventDO, exception);
                    return createFailureResult(exception);
            }
            
        } catch (RuntimeException e) {
            // 对于运行时异常（包括THROW_EXCEPTION策略抛出的异常），重新抛出
            throw e;
        } catch (Exception e) {
            log.error("降级处理异常，事件ID：{}，错误：{}",
                alertEventDO != null ? alertEventDO.getId() : "unknown", e.getMessage(), e);
            return createFailureResult(e);
        }
    }

    /**
     * 处理批量评估异常
     * 
     * @param alertEvents 预警事件列表
     * @param exception 异常信息
     * @return 降级处理结果列表
     */
    public List<RuleEvaluationResult> handleBatchEvaluationException(List<AlertEventDO> alertEvents, Exception exception) {
        List<RuleEvaluationResult> results = new ArrayList<>();
        
        if (alertEvents != null) {
            for (AlertEventDO alertEvent : alertEvents) {
                RuleEvaluationResult result = handleEvaluationException(alertEvent, exception);
                results.add(result);
            }
        }
        
        return results;
    }

    /**
     * 提取异常错误代码
     * 
     * @param exception 异常
     * @return 错误代码
     */
    private String extractErrorCode(Exception exception) {
        if (exception instanceof RuleEngineException) {
            return ((RuleEngineException) exception).getErrorCode();
        } else if (exception instanceof IllegalArgumentException) {
            return "VALIDATION_ERROR";
        } else if (exception.getMessage() != null && exception.getMessage().contains("数据库")) {
            return "DATABASE_ERROR";
        } else if (exception.getMessage() != null && exception.getMessage().contains("解析")) {
            return "PARSE_ERROR";
        } else {
            return "SYSTEM_ERROR";
        }
    }

    /**
     * 获取降级策略
     * 
     * @param errorCode 错误代码
     * @return 降级策略
     */
    private FallbackStrategy getFallbackStrategy(String errorCode) {
        return fallbackStrategies.getOrDefault(errorCode, fallbackStrategies.get("DEFAULT"));
    }

    /**
     * 创建失败结果
     * 
     * @param exception 异常
     * @return 失败结果
     */
    private RuleEvaluationResult createFailureResult(Exception exception) {
        String reason = "规则评估异常";
        if (exception != null && exception.getMessage() != null) {
            reason = "规则评估异常：" + exception.getMessage();
        }
        return RuleEvaluationResult.failure(reason);
    }

    /**
     * 创建默认成功结果
     * 
     * @return 默认成功结果
     */
    private RuleEvaluationResult createDefaultSuccessResult() {
        // 创建一个默认的成功结果，用于降级场景
        return RuleEvaluationResult.success(-1L, "DEFAULT_RULE", "DEFAULT", "LOW");
    }

    /**
     * 记录异常日志
     * 
     * @param alertEventDO 预警事件
     * @param exception 异常
     */
    private void logException(AlertEventDO alertEventDO, Exception exception) {
        log.error("规则评估异常详情，事件ID：{}，事件编号：{}，异常类型：{}，异常信息：{}", 
            alertEventDO != null ? alertEventDO.getId() : "unknown",
            alertEventDO != null ? alertEventDO.getEventId() : "unknown",
            exception.getClass().getSimpleName(),
            exception.getMessage(),
            exception);
    }

    /**
     * 设置降级策略
     * 
     * @param errorCode 错误代码
     * @param strategy 降级策略
     */
    public void setFallbackStrategy(String errorCode, FallbackStrategy strategy) {
        fallbackStrategies.put(errorCode, strategy);
        log.info("设置降级策略，错误代码：{}，策略：{}", errorCode, strategy);
    }

    /**
     * 获取当前降级策略配置
     * 
     * @return 降级策略配置
     */
    public Map<String, FallbackStrategy> getFallbackStrategies() {
        return new HashMap<>(fallbackStrategies);
    }

    /**
     * 检查是否应该启用降级
     * 
     * @param exception 异常
     * @return 是否启用降级
     */
    public boolean shouldFallback(Exception exception) {
        // 对于参数验证异常，通常不需要降级，直接抛出
        if (exception instanceof IllegalArgumentException) {
            return false;
        }
        
        // 对于规则引擎异常，根据类型判断
        if (exception instanceof RuleEngineException) {
            RuleEngineException ruleException = (RuleEngineException) exception;
            return ruleException.getType() != RuleEngineException.ExceptionType.VALIDATION_ERROR;
        }
        
        // 其他异常默认启用降级
        return true;
    }

    /**
     * 重置为默认降级策略
     */
    public void resetToDefault() {
        fallbackStrategies.clear();
        initDefaultFallbackStrategies();
        log.info("重置为默认降级策略");
    }
}
