package com.tool.converge.business.alert.rule;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * PayloadParser 单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("事件数据解析器测试")
class PayloadParserTest {

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private PayloadParser payloadParser;

    private String validPayloadJson;
    private String invalidPayloadJson;

    @BeforeEach
    void setUp() {
        validPayloadJson = "[{\"code\":\"fund_provider_name\",\"name\":\"资方名称\",\"value\":\"测试资方\"},{\"code\":\"hourly_total_orders\",\"name\":\"小时终态订单总数\",\"value\":\"3000\"}]";
        invalidPayloadJson = "invalid json";
    }

    @Test
    @DisplayName("测试正常JSON格式的解析")
    void testParsePayload_ValidJson() throws Exception {
        // 模拟ObjectMapper的行为
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方"),
                createPayloadItem("hourly_total_orders", "小时终态订单总数", "3000")
            ));

        Map<String, String> result = payloadParser.parsePayload(validPayloadJson);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试资方", result.get("fund_provider_name"));
        assertEquals("3000", result.get("hourly_total_orders"));
    }

    @Test
    @DisplayName("测试异常JSON格式的处理")
    void testParsePayload_InvalidJson() throws Exception {
        // 模拟JSON解析异常
        when(objectMapper.readValue(eq(invalidPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenThrow(new RuntimeException("JSON解析失败"));

        Map<String, String> result = payloadParser.parsePayload(invalidPayloadJson);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试空值和null值的处理")
    void testParsePayload_NullAndEmpty() {
        // 测试null值
        Map<String, String> result1 = payloadParser.parsePayload(null);
        assertNotNull(result1);
        assertTrue(result1.isEmpty());

        // 测试空字符串
        Map<String, String> result2 = payloadParser.parsePayload("");
        assertNotNull(result2);
        assertTrue(result2.isEmpty());

        // 测试空白字符串
        Map<String, String> result3 = payloadParser.parsePayload("   ");
        assertNotNull(result3);
        assertTrue(result3.isEmpty());
    }

    @Test
    @DisplayName("测试空PayloadItem列表的处理")
    void testParsePayload_EmptyList() throws Exception {
        String emptyListJson = "[]";
        when(objectMapper.readValue(eq(emptyListJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList());

        Map<String, String> result = payloadParser.parsePayload(emptyListJson);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试包含null项的PayloadItem列表")
    void testParsePayload_WithNullItems() throws Exception {
        String jsonWithNulls = "[{\"code\":\"valid_code\",\"name\":\"有效项\",\"value\":\"test\"},{\"code\":\"\",\"name\":\"无效项\",\"value\":\"invalid\"}]";
        when(objectMapper.readValue(eq(jsonWithNulls), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("valid_code", "有效项", "test"),
                createPayloadItem("", "无效项", "invalid")  // code为空的项
            ));

        Map<String, String> result = payloadParser.parsePayload(jsonWithNulls);

        assertNotNull(result);
        assertEquals(1, result.size());  // 只有有效的项被添加
        assertEquals("test", result.get("valid_code"));
        assertNull(result.get(""));  // 空code的项不会被添加
    }

    @Test
    @DisplayName("测试getValue方法")
    void testGetValue() throws Exception {
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方"),
                createPayloadItem("hourly_total_orders", "小时终态订单总数", "3000")
            ));

        Map<String, String> payloadMap = payloadParser.parsePayload(validPayloadJson);

        // 测试正常获取
        assertEquals("测试资方", payloadParser.getValue(payloadMap, "fund_provider_name"));
        assertEquals("3000", payloadParser.getValue(payloadMap, "hourly_total_orders"));

        // 测试不存在的key
        assertNull(payloadParser.getValue(payloadMap, "non_existent_key"));

        // 测试null参数
        assertNull(payloadParser.getValue(null, "fund_provider_name"));
        assertNull(payloadParser.getValue(payloadMap, null));
        assertNull(payloadParser.getValue(payloadMap, ""));
    }

    @Test
    @DisplayName("测试containsCode方法")
    void testContainsCode() throws Exception {
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方"),
                createPayloadItem("hourly_total_orders", "小时终态订单总数", "3000")
            ));

        Map<String, String> payloadMap = payloadParser.parsePayload(validPayloadJson);

        // 测试存在的code
        assertTrue(payloadParser.containsCode(payloadMap, "fund_provider_name"));
        assertTrue(payloadParser.containsCode(payloadMap, "hourly_total_orders"));

        // 测试不存在的code
        assertFalse(payloadParser.containsCode(payloadMap, "non_existent_code"));

        // 测试null参数
        assertFalse(payloadParser.containsCode(null, "fund_provider_name"));
        assertFalse(payloadParser.containsCode(payloadMap, null));
        assertFalse(payloadParser.containsCode(payloadMap, ""));
    }

    @Test
    @DisplayName("测试getAllCodes方法")
    void testGetAllCodes() throws Exception {
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方"),
                createPayloadItem("hourly_total_orders", "小时终态订单总数", "3000")
            ));

        Map<String, String> payloadMap = payloadParser.parsePayload(validPayloadJson);
        List<String> codes = payloadParser.getAllCodes(payloadMap);

        assertNotNull(codes);
        assertEquals(2, codes.size());
        assertTrue(codes.contains("fund_provider_name"));
        assertTrue(codes.contains("hourly_total_orders"));

        // 测试空Map
        List<String> emptyCodes = payloadParser.getAllCodes(null);
        assertNotNull(emptyCodes);
        assertTrue(emptyCodes.isEmpty());
    }

    @Test
    @DisplayName("测试validatePayload方法 - 验证通过")
    void testValidatePayload_Success() throws Exception {
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方"),
                createPayloadItem("hourly_total_orders", "小时终态订单总数", "3000")
            ));

        Map<String, String> payloadMap = payloadParser.parsePayload(validPayloadJson);
        List<String> requiredCodes = Arrays.asList("fund_provider_name", "hourly_total_orders");

        PayloadParser.PayloadValidationResult result = payloadParser.validatePayload(payloadMap, requiredCodes);

        assertTrue(result.isValid());
        assertEquals("Payload数据验证通过", result.getMessage());
        assertTrue(result.getMissingCodes().isEmpty());
    }

    @Test
    @DisplayName("测试validatePayload方法 - 缺少必需字段")
    void testValidatePayload_MissingFields() throws Exception {
        when(objectMapper.readValue(eq(validPayloadJson), any(com.fasterxml.jackson.core.type.TypeReference.class)))
            .thenReturn(Arrays.asList(
                createPayloadItem("fund_provider_name", "资方名称", "测试资方")
            ));

        Map<String, String> payloadMap = payloadParser.parsePayload(validPayloadJson);
        List<String> requiredCodes = Arrays.asList("fund_provider_name", "hourly_total_orders", "missing_field");

        PayloadParser.PayloadValidationResult result = payloadParser.validatePayload(payloadMap, requiredCodes);

        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("缺少必需字段"));
        assertEquals(2, result.getMissingCodes().size());
        assertTrue(result.getMissingCodes().contains("hourly_total_orders"));
        assertTrue(result.getMissingCodes().contains("missing_field"));
    }

    @Test
    @DisplayName("测试validatePayload方法 - 空数据")
    void testValidatePayload_EmptyData() {
        List<String> requiredCodes = Arrays.asList("fund_provider_name");

        // 测试null payloadMap
        PayloadParser.PayloadValidationResult result1 = payloadParser.validatePayload(null, requiredCodes);
        assertFalse(result1.isValid());
        assertEquals("Payload数据为空", result1.getMessage());

        // 测试无必需字段
        PayloadParser.PayloadValidationResult result2 = payloadParser.validatePayload(new java.util.HashMap<>(), null);
        assertTrue(result2.isValid());
        assertEquals("无必需字段要求", result2.getMessage());
    }

    /**
     * 创建PayloadItem的辅助方法
     */
    private com.tool.converge.repository.domain.alert.bo.AlertEventReportBO.PayloadItem createPayloadItem(String code, String name, String value) {
        return com.tool.converge.repository.domain.alert.bo.AlertEventReportBO.PayloadItem.builder()
            .code(code)
            .name(name)
            .value(value)
            .build();
    }
}
