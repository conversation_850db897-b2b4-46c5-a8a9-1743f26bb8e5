package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.ConditionEvaluationDetail;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConditionEvaluator单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class ConditionEvaluatorTest {

    @InjectMocks
    private ConditionEvaluator conditionEvaluator;

    private Map<String, String> payloadMap;
    private WarnConditionDO condition;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        payloadMap = new HashMap<>();
        payloadMap.put("amount", "100.50");
        payloadMap.put("status", "ACTIVE");
        payloadMap.put("count", "5");

        condition = WarnConditionDO.builder()
            .id(1L)
            .rulesId(1L)
            .settingItem("amount")
            .operator("GT")
            .assignmentItem(new BigDecimal("50.00"))
            .build();
    }

    @Test
    void testEvaluateCondition_GT_Success() {
        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_GT_Failure() {
        // 修改条件为大于200
        condition.setAssignmentItem(new BigDecimal("200.00"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_GTE_Success() {
        // 修改运算符为大于等于
        condition.setOperator("GTE");
        condition.setAssignmentItem(new BigDecimal("100.50"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_EQ_Success() {
        // 修改运算符为等于
        condition.setOperator("EQ");
        condition.setAssignmentItem(new BigDecimal("100.50"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_LT_Success() {
        // 修改运算符为小于
        condition.setOperator("LT");
        condition.setAssignmentItem(new BigDecimal("200.00"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_LTE_Success() {
        // 修改运算符为小于等于
        condition.setOperator("LTE");
        condition.setAssignmentItem(new BigDecimal("100.50"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_NE_Success() {
        // 修改运算符为不等于
        condition.setOperator("NE");
        condition.setAssignmentItem(new BigDecimal("50.00"));

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_StringComparison() {
        // 修改为字符串比较
        condition.setSettingItem("status");
        condition.setOperator("EQ");
        condition.setAssignmentItem(new BigDecimal("0")); // 这里会转换为字符串"0"进行比较

        // 修改payload中的值为"0"以匹配
        payloadMap.put("status", "0");

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testEvaluateCondition_NullCondition() {
        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(null, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_EmptySettingItem() {
        // 设置空的设置项
        condition.setSettingItem("");

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_EmptyOperator() {
        // 设置空的运算符
        condition.setOperator("");

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_NullAssignmentItem() {
        // 设置空的赋值项
        condition.setAssignmentItem(null);

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_SettingItemNotInPayload() {
        // 设置payload中不存在的设置项
        condition.setSettingItem("nonexistent");

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateCondition_UnsupportedOperator() {
        // 设置不支持的运算符
        condition.setOperator("INVALID");

        // 执行测试
        boolean result = conditionEvaluator.evaluateCondition(condition, payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testEvaluateConditionWithDetail_Success() {
        // 执行测试
        ConditionEvaluationDetail result = conditionEvaluator.evaluateConditionWithDetail(condition, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isResult());
        assertEquals(condition.getId(), result.getConditionId());
        assertEquals(condition.getSettingItem(), result.getSettingItem());
        assertEquals(condition.getOperator(), result.getOperator());
        assertEquals("50.00", result.getExpectedValue());
        assertEquals("100.50", result.getActualValue());
        assertNotNull(result.getMessage());
        assertFalse(result.hasError());
        assertNotNull(result.getEvaluationStartTime());
        assertNotNull(result.getEvaluationEndTime());
        assertNotNull(result.getEvaluationDuration());
    }

    @Test
    void testEvaluateConditionWithDetail_Failure() {
        // 修改条件为大于200
        condition.setAssignmentItem(new BigDecimal("200.00"));

        // 执行测试
        ConditionEvaluationDetail result = conditionEvaluator.evaluateConditionWithDetail(condition, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isResult());
        assertEquals(condition.getId(), result.getConditionId());
        assertEquals("200.00", result.getExpectedValue());
        assertEquals("100.50", result.getActualValue());
        assertFalse(result.hasError());
    }

    @Test
    void testEvaluateConditionWithDetail_Error() {
        // 设置空的设置项导致错误
        condition.setSettingItem("");

        // 执行测试
        ConditionEvaluationDetail result = conditionEvaluator.evaluateConditionWithDetail(condition, payloadMap);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isResult());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("设置项为空"));
    }

    @Test
    void testIsValidCondition_Valid() {
        // 执行测试
        boolean result = conditionEvaluator.isValidCondition(condition);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsValidCondition_NullCondition() {
        // 执行测试
        boolean result = conditionEvaluator.isValidCondition(null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidCondition_EmptySettingItem() {
        // 设置空的设置项
        condition.setSettingItem("");

        // 执行测试
        boolean result = conditionEvaluator.isValidCondition(condition);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsValidCondition_UnsupportedOperator() {
        // 设置不支持的运算符
        condition.setOperator("INVALID");

        // 执行测试
        boolean result = conditionEvaluator.isValidCondition(condition);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testHasSettingItem_Exists() {
        // 执行测试
        boolean result = conditionEvaluator.hasSettingItem("amount", payloadMap);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testHasSettingItem_NotExists() {
        // 执行测试
        boolean result = conditionEvaluator.hasSettingItem("nonexistent", payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testHasSettingItem_EmptySettingItem() {
        // 执行测试
        boolean result = conditionEvaluator.hasSettingItem("", payloadMap);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testGetSettingItemValue_Exists() {
        // 执行测试
        String result = conditionEvaluator.getSettingItemValue("amount", payloadMap);

        // 验证结果
        assertEquals("100.50", result);
    }

    @Test
    void testGetSettingItemValue_NotExists() {
        // 执行测试
        String result = conditionEvaluator.getSettingItemValue("nonexistent", payloadMap);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testIsSupportedOperator_Supported() {
        // 执行测试
        boolean result = conditionEvaluator.isSupportedOperator("GT");

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsSupportedOperator_NotSupported() {
        // 执行测试
        boolean result = conditionEvaluator.isSupportedOperator("INVALID");

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testDataTypeDetection_Numeric() {
        // 执行测试
        ConditionEvaluationDetail result = conditionEvaluator.evaluateConditionWithDetail(condition, payloadMap);

        // 验证结果
        assertEquals("NUMERIC", result.getDataType());
        assertFalse(result.isTypeConverted());
    }

    @Test
    void testDataTypeDetection_String() {
        // 修改为字符串比较
        condition.setSettingItem("status");
        condition.setOperator("EQ");
        condition.setAssignmentItem(new BigDecimal("1")); // 这个会被转换为字符串

        // 修改payload中的值为非数字字符串
        payloadMap.put("status", "ACTIVE");

        // 执行测试
        ConditionEvaluationDetail result = conditionEvaluator.evaluateConditionWithDetail(condition, payloadMap);

        // 验证结果
        assertEquals("STRING", result.getDataType());
        assertFalse(result.isTypeConverted());
    }
}
