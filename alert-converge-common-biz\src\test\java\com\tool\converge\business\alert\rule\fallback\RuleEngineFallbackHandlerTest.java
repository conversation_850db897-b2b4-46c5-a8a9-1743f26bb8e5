package com.tool.converge.business.alert.rule.fallback;

import com.tool.converge.business.alert.rule.exception.RuleEngineException;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RuleEngineFallbackHandler单元测试
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class RuleEngineFallbackHandlerTest {

    @InjectMocks
    private RuleEngineFallbackHandler fallbackHandler;

    private AlertEventDO alertEventDO;

    @BeforeEach
    void setUp() {
        alertEventDO = AlertEventDO.builder()
            .id(1L)
            .eventId("EVENT_001")
            .payload("{\"fund_provider_name\":\"测试资方\"}")
            .build();
    }

    @Test
    void testHandleEvaluationException_ParseError() {
        // 创建解析异常
        RuleEngineException exception = RuleEngineException.parseError("JSON解析失败");

        // 执行测试
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(alertEventDO, exception);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
        assertTrue(result.getFailureReason().contains("规则评估异常"));
    }

    @Test
    void testHandleEvaluationException_DatabaseError() {
        // 创建数据库异常
        RuleEngineException exception = RuleEngineException.databaseError("数据库连接失败");

        // 执行测试
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(alertEventDO, exception);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
    }

    @Test
    void testHandleEvaluationException_ValidationError() {
        // 创建参数验证异常
        RuleEngineException exception = RuleEngineException.validationError("参数不能为空");

        // 执行测试并验证抛出异常
        assertThrows(RuleEngineException.class, () -> {
            fallbackHandler.handleEvaluationException(alertEventDO, exception);
        });
    }

    @Test
    void testHandleEvaluationException_SystemError() {
        // 创建系统异常
        RuntimeException exception = new RuntimeException("系统异常");

        // 执行测试
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(alertEventDO, exception);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
    }

    @Test
    void testHandleBatchEvaluationException() {
        // 创建事件列表
        List<AlertEventDO> alertEvents = new ArrayList<>();
        alertEvents.add(alertEventDO);
        alertEvents.add(AlertEventDO.builder().id(2L).eventId("EVENT_002").build());

        // 创建异常
        RuleEngineException exception = RuleEngineException.systemError("批量处理异常");

        // 执行测试
        List<RuleEvaluationResult> results = fallbackHandler.handleBatchEvaluationException(alertEvents, exception);

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        for (RuleEvaluationResult result : results) {
            assertFalse(result.isMatched());
            assertNotNull(result.getFailureReason());
        }
    }

    @Test
    void testShouldFallback_ValidationError() {
        // 参数验证异常不应该降级
        IllegalArgumentException exception = new IllegalArgumentException("参数错误");
        
        boolean result = fallbackHandler.shouldFallback(exception);
        
        assertFalse(result);
    }

    @Test
    void testShouldFallback_RuleEngineValidationError() {
        // 规则引擎的参数验证异常不应该降级
        RuleEngineException exception = RuleEngineException.validationError("参数验证失败");
        
        boolean result = fallbackHandler.shouldFallback(exception);
        
        assertFalse(result);
    }

    @Test
    void testShouldFallback_SystemError() {
        // 系统异常应该降级
        RuntimeException exception = new RuntimeException("系统异常");
        
        boolean result = fallbackHandler.shouldFallback(exception);
        
        assertTrue(result);
    }

    @Test
    void testShouldFallback_RuleEngineSystemError() {
        // 规则引擎的系统异常应该降级
        RuleEngineException exception = RuleEngineException.systemError("系统异常");
        
        boolean result = fallbackHandler.shouldFallback(exception);
        
        assertTrue(result);
    }

    @Test
    void testSetFallbackStrategy() {
        // 设置新的降级策略
        fallbackHandler.setFallbackStrategy("TEST_ERROR", RuleEngineFallbackHandler.FallbackStrategy.RETURN_DEFAULT_SUCCESS);

        // 获取策略配置
        Map<String, RuleEngineFallbackHandler.FallbackStrategy> strategies = fallbackHandler.getFallbackStrategies();
        
        // 验证策略已设置
        assertEquals(RuleEngineFallbackHandler.FallbackStrategy.RETURN_DEFAULT_SUCCESS, strategies.get("TEST_ERROR"));
    }

    @Test
    void testGetFallbackStrategies() {
        // 获取降级策略配置
        Map<String, RuleEngineFallbackHandler.FallbackStrategy> strategies = fallbackHandler.getFallbackStrategies();

        // 验证默认策略存在
        assertNotNull(strategies);
        assertTrue(strategies.containsKey("PARSE_ERROR"));
        assertTrue(strategies.containsKey("DATABASE_ERROR"));
        assertTrue(strategies.containsKey("SYSTEM_ERROR"));
        assertTrue(strategies.containsKey("VALIDATION_ERROR"));
        assertTrue(strategies.containsKey("DEFAULT"));
    }

    @Test
    void testResetToDefault() {
        // 先设置一个自定义策略
        fallbackHandler.setFallbackStrategy("CUSTOM_ERROR", RuleEngineFallbackHandler.FallbackStrategy.RETURN_DEFAULT_SUCCESS);
        
        // 重置为默认策略
        fallbackHandler.resetToDefault();
        
        // 验证自定义策略已被清除
        Map<String, RuleEngineFallbackHandler.FallbackStrategy> strategies = fallbackHandler.getFallbackStrategies();
        assertFalse(strategies.containsKey("CUSTOM_ERROR"));
        
        // 验证默认策略仍然存在
        assertTrue(strategies.containsKey("DEFAULT"));
    }

    @Test
    void testHandleEvaluationException_ReturnDefaultSuccess() {
        // 设置返回默认成功的策略
        fallbackHandler.setFallbackStrategy("PARSE_ERROR", RuleEngineFallbackHandler.FallbackStrategy.RETURN_DEFAULT_SUCCESS);
        
        // 创建解析异常
        RuleEngineException exception = RuleEngineException.parseError("JSON解析失败");

        // 执行测试
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(alertEventDO, exception);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isMatched());
        assertEquals(-1L, result.getMatchedRuleId());
        assertEquals("DEFAULT_RULE", result.getMatchedRuleName());
        assertEquals("DEFAULT", result.getMatchedRuleCode());
    }

    @Test
    void testHandleEvaluationException_ThrowException() {
        // 设置抛出异常的策略
        fallbackHandler.setFallbackStrategy("PARSE_ERROR", RuleEngineFallbackHandler.FallbackStrategy.THROW_EXCEPTION);
        
        // 创建解析异常
        RuleEngineException exception = RuleEngineException.parseError("JSON解析失败");

        // 执行测试并验证抛出异常
        assertThrows(RuleEngineException.class, () -> {
            fallbackHandler.handleEvaluationException(alertEventDO, exception);
        });
    }

    @Test
    void testHandleEvaluationException_NullAlertEvent() {
        // 创建异常
        RuleEngineException exception = RuleEngineException.systemError("系统异常");

        // 执行测试（传入null事件）
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(null, exception);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
    }

    @Test
    void testHandleEvaluationException_FallbackException() {
        // 设置一个会导致降级处理异常的场景
        fallbackHandler.setFallbackStrategy("PARSE_ERROR", null); // 这会导致NullPointerException
        
        // 创建解析异常
        RuleEngineException exception = RuleEngineException.parseError("JSON解析失败");

        // 执行测试
        RuleEvaluationResult result = fallbackHandler.handleEvaluationException(alertEventDO, exception);

        // 验证结果（应该返回失败结果，而不是抛出异常）
        assertNotNull(result);
        assertFalse(result.isMatched());
        assertNotNull(result.getFailureReason());
    }
}
